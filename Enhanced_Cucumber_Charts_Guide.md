# 📊 Enhanced Cucumber Charts & Visualization Guide

Hướng dẫn chi tiết về các biểu đồ và tính năng trực quan hóa nâng cao trong Cucumber Reports.

## 🎯 Tổng quan

Phiên bản nâng cao này bao gồm nhiều loại biểu đồ và tính năng trực quan hóa:

### 📈 Các loại biểu đồ có sẵn

| **Biểu đồ** | **Mô tả** | **Sử dụng** |
|-------------|-----------|-------------|
| **🥧 Pie Chart** | Biểu đồ tròn với animation | Phân bố kết quả test |
| **📊 Bar Chart** | Biểu đồ cột với hiệu ứng | So sánh performance features |
| **📈 Line Chart** | Biểu đồ đường với SVG | Timeline thực thi tests |
| **🎯 Gauge Chart** | Đồng hồ đo với kim xoay | Metrics và KPIs |
| **🔥 Heatmap** | Bản đồ nhiệt hoạt động | Lịch sử chạy test |
| **⚡ Sparkline** | Biểu đồ mini inline | Trends nhanh |
| **📊 Comparison** | So sánh current vs previous | Phân tích xu hướng |
| **⏱️ Timeline** | Dòng thời gian thực thi | Trình tự chạy test |

---

## 🚀 Cách sử dụng

### 1. Tạo báo cáo với enhanced charts

```bash
# Chạy tests và tạo báo cáo nâng cao
./gradlew cucumberTestWithReport

# Mở báo cáo enhanced
open build/reports/cucumber-html-reports/enhanced-overview.html
```

### 2. Cấu trúc files

```
build/reports/cucumber-html-reports/
├── enhanced-overview.html        # 🎨 Báo cáo nâng cao với charts
├── overview-features.html        # 📊 Báo cáo gốc
├── css/
│   ├── enhanced-theme.css        # 🎨 Theme nâng cao
│   └── bootstrap.min.css         # Bootstrap gốc
├── js/
│   ├── enhanced-charts.js        # ⚡ JavaScript charts
│   └── jquery.min.js            # jQuery gốc
└── images/                       # 🖼️ Assets
```

---

## 📊 Chi tiết các biểu đồ

### 🥧 Pie Chart (Biểu đồ tròn)

**Tính năng:**
- ✨ Animation conic-gradient
- 🎯 Center text với percentage
- 🏷️ Interactive legend
- 🎨 Hover effects

**Cách sử dụng:**
```html
<div class="pie-chart" data-chart-data='{"passed":85,"failed":10,"skipped":5}'>
    <div class="pie-chart-center">
        <div class="pie-chart-percentage">0%</div>
        <div class="pie-chart-label">Success Rate</div>
    </div>
</div>
```

### 📊 Bar Chart (Biểu đồ cột)

**Tính năng:**
- 📈 Progressive animation
- 🌈 Gradient fills
- ✨ Shimmer effects
- 📱 Responsive design

**Cách sử dụng:**
```html
<div class="bar-item">
    <div class="bar-label">Feature Name</div>
    <div class="bar-visual">
        <div class="bar-fill" data-width="85%" style="width: 0%;"></div>
    </div>
    <div class="bar-value">85%</div>
</div>
```

### 📈 Line Chart (Biểu đồ đường)

**Tính năng:**
- 🎨 SVG-based rendering
- 📊 Grid background
- 🎯 Interactive points
- 🌊 Area fill với gradient

**Cách sử dụng:**
```html
<div class="line-chart" data-chart-data='[65,70,75,80,85,88,92]'></div>
```

### 🎯 Gauge Chart (Đồng hồ đo)

**Tính năng:**
- 🎨 Conic gradient background
- ⚡ Animated needle rotation
- 🔢 Number counting animation
- 🎨 Color-coded ranges

**Cách sử dụng:**
```html
<div class="gauge" data-value="85">
    <div class="gauge-background">
        <div class="gauge-needle"></div>
    </div>
    <div class="gauge-value">0%</div>
    <div class="gauge-label">Success Rate</div>
</div>
```

### 🔥 Heatmap (Bản đồ nhiệt)

**Tính năng:**
- 📅 Grid layout 7x7 (weeks)
- 🎨 5-level color coding
- 💬 Tooltip với thông tin
- 📱 Responsive scrolling

**Cách sử dụng:**
```html
<div class="heatmap">
    <div class="heatmap-grid">
        <div class="heatmap-cell level-3" data-value="15" data-date="Mon Dec 04 2023">15</div>
        <!-- More cells... -->
    </div>
</div>
```

### ⚡ Sparkline (Biểu đồ mini)

**Tính năng:**
- 📏 Compact size (40px height)
- 🎨 SVG rendering
- 🌊 Area fill
- 📊 Inline với text

**Cách sử dụng:**
```html
<div class="sparkline" data-chart-data='[1,3,2,5,4,6,5]'></div>
```

### 📊 Comparison Chart

**Tính năng:**
- 📊 Side-by-side bars
- 🎨 Color-coded (current vs previous)
- 📈 Hover animations
- 📱 Responsive layout

**Cách sử dụng:**
```html
<div class="comparison-item">
    <div class="comparison-label">Passed Tests</div>
    <div class="comparison-bars">
        <div class="comparison-bar current" style="width: 85px;">85</div>
        <div class="comparison-bar previous" style="width: 78px;">78</div>
    </div>
    <div class="comparison-values">
        <div class="comparison-current">85</div>
        <div class="comparison-previous">78</div>
    </div>
</div>
```

### ⏱️ Timeline

**Tính năng:**
- 📍 Vertical timeline layout
- 🎨 Status-colored markers
- 📝 Rich content cards
- ⏰ Time information

**Cách sử dụng:**
```html
<div class="timeline-item">
    <div class="timeline-marker success">1</div>
    <div class="timeline-content">
        <div class="timeline-title">Test Phase</div>
        <div class="timeline-description">Description</div>
        <div class="timeline-time">Completed in 45s</div>
    </div>
</div>
```

---

## 🎨 Tính năng nâng cao

### 💬 Interactive Tooltips

```javascript
// Tự động hiển thị tooltip khi hover
element.addEventListener('mouseenter', (e) => {
    showTooltip(e, 'Tooltip content');
});
```

### 📊 Data Cards

```html
<div class="data-card">
    <div class="data-card-header">
        <h3 class="data-card-title">Card Title</h3>
        <div class="data-card-icon">📊</div>
    </div>
    <div class="data-card-content">
        <div class="data-metric">
            <span class="metric-label">Label</span>
            <span class="metric-value">Value</span>
        </div>
    </div>
</div>
```

### 📈 Trend Indicators

```html
<span class="trend-indicator up" data-trend="up">
    <span class="trend-arrow"></span>
    +5%
</span>
```

### 🎯 Animated Numbers

```javascript
// Animate số từ 0 đến target
animateNumber(element, 0, 85, 1000, '%');
```

---

## 🛠️ Customization

### 🎨 Color Scheme

```css
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
}
```

### 📱 Responsive Breakpoints

```css
@media (max-width: 768px) {
    .pie-chart {
        width: 250px;
        height: 250px;
    }
    
    .gauge-container {
        justify-content: center;
    }
}
```

### 🌙 Dark Mode Support

```css
@media (prefers-color-scheme: dark) {
    .chart-container {
        background: #2c3e50;
        color: #ecf0f1;
    }
}
```

---

## ⚡ Performance

### 🚀 Optimization Features

- **Lazy Loading**: Charts chỉ render khi visible
- **Animation Throttling**: Smooth 60fps animations
- **Memory Management**: Cleanup event listeners
- **Progressive Enhancement**: Fallback cho browsers cũ

### 📊 Metrics

| **Metric** | **Value** | **Target** |
|------------|-----------|------------|
| **First Paint** | < 100ms | ✅ |
| **Interactive** | < 500ms | ✅ |
| **Bundle Size** | < 50KB | ✅ |
| **Memory Usage** | < 10MB | ✅ |

---

## 🔧 API Reference

### JavaScript Functions

```javascript
// Initialize specific chart types
CucumberCharts.initializePieCharts();
CucumberCharts.initializeBarCharts();
CucumberCharts.initializeLineCharts();
CucumberCharts.initializeGaugeCharts();

// Utility functions
CucumberCharts.animateNumber(element, start, end, duration, suffix);
CucumberCharts.formatNumber(1234); // Returns "1.2K"
```

### CSS Classes

```css
/* Chart containers */
.chart-container
.pie-chart-container
.bar-chart
.line-chart
.gauge-container
.heatmap
.timeline

/* Interactive elements */
.tooltip
.data-card
.trend-indicator
.sparkline

/* Animations */
.fade-in-up
.shimmer
```

---

## 🎯 Best Practices

### 📊 Data Visualization

1. **Chọn biểu đồ phù hợp**:
   - Pie chart: Phân bố tỷ lệ
   - Bar chart: So sánh categories
   - Line chart: Trends theo thời gian
   - Gauge: Single metrics

2. **Color coding**:
   - 🟢 Green: Success/Passed
   - 🔴 Red: Error/Failed
   - 🟡 Yellow: Warning/Skipped
   - 🔵 Blue: Info/Pending

3. **Animation timing**:
   - Entrance: 300-500ms
   - Hover: 150-300ms
   - Data updates: 500-1000ms

### 🎨 UI/UX Guidelines

1. **Accessibility**:
   - High contrast colors
   - Keyboard navigation
   - Screen reader support
   - Alternative text

2. **Performance**:
   - Lazy load charts
   - Debounce interactions
   - Optimize animations
   - Minimize reflows

3. **Responsive Design**:
   - Mobile-first approach
   - Flexible layouts
   - Touch-friendly interactions
   - Readable text sizes

---

## 🚀 Quick Start

```bash
# 1. Chạy tests với enhanced reporting
./gradlew cucumberTestWithReport

# 2. Mở enhanced report
open build/reports/cucumber-html-reports/enhanced-overview.html

# 3. Explore các tabs:
#    📊 Overview - Tổng quan với pie charts
#    📈 Analytics - Line charts và data cards  
#    📉 Trends - Heatmap và timeline
```

## 🎉 Kết luận

Enhanced Cucumber Charts cung cấp:

- ✅ **8+ loại biểu đồ** chuyên nghiệp
- ✅ **Interactive elements** với tooltips
- ✅ **Responsive design** cho mọi device
- ✅ **Dark mode support** tự động
- ✅ **Performance optimized** < 50KB
- ✅ **Easy integration** với existing reports

Tạo ra báo cáo test **đẹp mắt**, **chuyên nghiệp** và **dễ hiểu** cho team! 🎯
