/**
 * Enhanced Charts JavaScript for Cucumber Reports
 * Provides interactive chart functionality and data visualization
 */

// Chart data and configuration
const ChartConfig = {
    colors: {
        primary: '#2c3e50',
        secondary: '#3498db',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        light: '#ecf0f1'
    },
    
    animations: {
        duration: 1000,
        easing: 'ease-in-out'
    }
};

/**
 * Initialize all charts when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    initializePieCharts();
    initializeBarCharts();
    initializeLineCharts();
    initializeGaugeCharts();
    initializeHeatmaps();
    initializeTooltips();
    initializeSparklines();
    initializeTrendIndicators();
});

/**
 * Initialize pie charts with animation
 */
function initializePieCharts() {
    const pieCharts = document.querySelectorAll('.pie-chart');
    
    pieCharts.forEach(chart => {
        const data = JSON.parse(chart.dataset.chartData || '{}');
        animatePieChart(chart, data);
    });
}

/**
 * Animate pie chart creation
 */
function animatePieChart(element, data) {
    const total = Object.values(data).reduce((sum, val) => sum + val, 0);
    let currentAngle = 0;
    
    // Create gradient for pie chart
    const gradients = [];
    Object.keys(data).forEach((key, index) => {
        const percentage = (data[key] / total) * 360;
        const color = getStatusColor(key);
        
        gradients.push(`${color} ${currentAngle}deg ${currentAngle + percentage}deg`);
        currentAngle += percentage;
    });
    
    // Apply animated gradient
    setTimeout(() => {
        element.style.background = `conic-gradient(${gradients.join(', ')})`;
        element.classList.add('fade-in-up');
    }, 100);
    
    // Update center text
    const centerText = element.querySelector('.pie-chart-percentage');
    if (centerText && data.passed) {
        const passRate = Math.round((data.passed / total) * 100);
        animateNumber(centerText, 0, passRate, 1000, '%');
    }
}

/**
 * Initialize bar charts with progressive animation
 */
function initializeBarCharts() {
    const barCharts = document.querySelectorAll('.bar-chart');
    
    barCharts.forEach(chart => {
        const bars = chart.querySelectorAll('.bar-fill');
        
        bars.forEach((bar, index) => {
            const width = bar.dataset.width || '0%';
            
            // Animate bar fill
            setTimeout(() => {
                bar.style.width = width;
                bar.classList.add('fade-in-up');
            }, index * 200);
        });
    });
}

/**
 * Initialize line charts with SVG animation
 */
function initializeLineCharts() {
    const lineCharts = document.querySelectorAll('.line-chart');
    
    lineCharts.forEach(chart => {
        const data = JSON.parse(chart.dataset.chartData || '[]');
        createSVGLineChart(chart, data);
    });
}

/**
 * Create animated SVG line chart
 */
function createSVGLineChart(container, data) {
    const width = container.clientWidth - 80;
    const height = container.clientHeight - 80;
    
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', width);
    svg.setAttribute('height', height);
    svg.style.position = 'absolute';
    svg.style.top = '30px';
    svg.style.left = '50px';
    
    // Create gradient definition
    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
    const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
    gradient.setAttribute('id', 'lineGradient');
    gradient.setAttribute('x1', '0%');
    gradient.setAttribute('y1', '0%');
    gradient.setAttribute('x2', '0%');
    gradient.setAttribute('y2', '100%');
    
    const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop1.setAttribute('offset', '0%');
    stop1.setAttribute('stop-color', ChartConfig.colors.secondary);
    stop1.setAttribute('stop-opacity', '0.8');
    
    const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop2.setAttribute('offset', '100%');
    stop2.setAttribute('stop-color', ChartConfig.colors.secondary);
    stop2.setAttribute('stop-opacity', '0.1');
    
    gradient.appendChild(stop1);
    gradient.appendChild(stop2);
    defs.appendChild(gradient);
    svg.appendChild(defs);
    
    // Create path for line
    if (data.length > 0) {
        const maxValue = Math.max(...data);
        const points = data.map((value, index) => {
            const x = (index / (data.length - 1)) * width;
            const y = height - (value / maxValue) * height;
            return `${x},${y}`;
        }).join(' ');
        
        // Create area path
        const areaPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        const areaPoints = `M0,${height} L${points} L${width},${height} Z`;
        areaPath.setAttribute('d', areaPoints);
        areaPath.setAttribute('class', 'line-chart-area');
        svg.appendChild(areaPath);
        
        // Create line path
        const linePath = document.createElementNS('http://www.w3.org/2000/svg', 'polyline');
        linePath.setAttribute('points', points);
        linePath.setAttribute('class', 'line-chart-path');
        svg.appendChild(linePath);
        
        // Add data points
        data.forEach((value, index) => {
            const x = (index / (data.length - 1)) * width;
            const y = height - (value / maxValue) * height;
            
            const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            circle.setAttribute('cx', x);
            circle.setAttribute('cy', y);
            circle.setAttribute('class', 'line-chart-point');
            circle.setAttribute('data-value', value);
            
            // Add hover effect
            circle.addEventListener('mouseenter', (e) => showTooltip(e, `Value: ${value}`));
            circle.addEventListener('mouseleave', hideTooltip);
            
            svg.appendChild(circle);
        });
    }
    
    container.appendChild(svg);
    
    // Animate line drawing
    const path = svg.querySelector('.line-chart-path');
    if (path) {
        const length = path.getTotalLength();
        path.style.strokeDasharray = length;
        path.style.strokeDashoffset = length;
        
        setTimeout(() => {
            path.style.transition = `stroke-dashoffset ${ChartConfig.animations.duration}ms ${ChartConfig.animations.easing}`;
            path.style.strokeDashoffset = 0;
        }, 500);
    }
}

/**
 * Initialize gauge charts
 */
function initializeGaugeCharts() {
    const gauges = document.querySelectorAll('.gauge');
    
    gauges.forEach(gauge => {
        const value = parseFloat(gauge.dataset.value || '0');
        const needle = gauge.querySelector('.gauge-needle');
        const valueDisplay = gauge.querySelector('.gauge-value');
        
        if (needle && valueDisplay) {
            // Calculate rotation angle (0-180 degrees)
            const angle = (value / 100) * 180 - 90;
            
            setTimeout(() => {
                needle.style.transform = `translateX(-50%) rotate(${angle}deg)`;
                animateNumber(valueDisplay, 0, value, 1500, '%');
            }, 300);
        }
    });
}

/**
 * Initialize heatmap interactions
 */
function initializeHeatmaps() {
    const heatmapCells = document.querySelectorAll('.heatmap-cell');
    
    heatmapCells.forEach(cell => {
        const value = cell.dataset.value || '0';
        const date = cell.dataset.date || '';
        
        cell.addEventListener('mouseenter', (e) => {
            showTooltip(e, `${date}: ${value} tests`);
        });
        
        cell.addEventListener('mouseleave', hideTooltip);
    });
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    document.body.appendChild(tooltip);
    
    window.showTooltip = function(event, text) {
        tooltip.textContent = text;
        tooltip.classList.add('show');
        
        const rect = event.target.getBoundingClientRect();
        tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    };
    
    window.hideTooltip = function() {
        tooltip.classList.remove('show');
    };
}

/**
 * Initialize sparkline charts
 */
function initializeSparklines() {
    const sparklines = document.querySelectorAll('.sparkline');
    
    sparklines.forEach(sparkline => {
        const data = JSON.parse(sparkline.dataset.chartData || '[]');
        createSparkline(sparkline, data);
    });
}

/**
 * Create SVG sparkline
 */
function createSparkline(container, data) {
    if (data.length === 0) return;
    
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('width', width);
    svg.setAttribute('height', height);
    
    const maxValue = Math.max(...data);
    const minValue = Math.min(...data);
    const range = maxValue - minValue || 1;
    
    const points = data.map((value, index) => {
        const x = (index / (data.length - 1)) * width;
        const y = height - ((value - minValue) / range) * height;
        return `${x},${y}`;
    }).join(' ');
    
    // Create area
    const areaPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    const areaPoints = `M0,${height} L${points} L${width},${height} Z`;
    areaPath.setAttribute('d', areaPoints);
    areaPath.setAttribute('class', 'sparkline-area');
    svg.appendChild(areaPath);
    
    // Create line
    const line = document.createElementNS('http://www.w3.org/2000/svg', 'polyline');
    line.setAttribute('points', points);
    line.setAttribute('class', 'sparkline-path');
    svg.appendChild(line);
    
    container.appendChild(svg);
}

/**
 * Initialize trend indicators
 */
function initializeTrendIndicators() {
    const indicators = document.querySelectorAll('.trend-indicator');
    
    indicators.forEach(indicator => {
        const trend = indicator.dataset.trend || 'stable';
        const arrow = indicator.querySelector('.trend-arrow');
        
        if (arrow) {
            switch (trend) {
                case 'up':
                    arrow.textContent = '↗';
                    break;
                case 'down':
                    arrow.textContent = '↘';
                    break;
                default:
                    arrow.textContent = '→';
            }
        }
        
        // Add entrance animation
        setTimeout(() => {
            indicator.classList.add('fade-in-up');
        }, Math.random() * 500);
    });
}

/**
 * Animate number counting
 */
function animateNumber(element, start, end, duration, suffix = '') {
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.round(start + (end - start) * easeOutCubic(progress));
        element.textContent = current + suffix;
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

/**
 * Easing function for smooth animations
 */
function easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
}

/**
 * Get color based on status
 */
function getStatusColor(status) {
    const colors = {
        passed: ChartConfig.colors.success,
        failed: ChartConfig.colors.danger,
        skipped: ChartConfig.colors.warning,
        pending: ChartConfig.colors.secondary
    };
    
    return colors[status] || ChartConfig.colors.secondary;
}

/**
 * Utility function to format numbers
 */
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

/**
 * Export functions for external use
 */
window.CucumberCharts = {
    initializePieCharts,
    initializeBarCharts,
    initializeLineCharts,
    initializeGaugeCharts,
    animateNumber,
    formatNumber
};
