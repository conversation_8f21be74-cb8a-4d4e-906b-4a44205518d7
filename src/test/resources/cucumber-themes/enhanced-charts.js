/**
 * Enhanced Charts JavaScript for Cucumber Reports
 * Provides interactive chart functionality and data visualization
 */

// Chart configuration
const ChartConfig = {
    colors: {
        primary: '#2c3e50',
        secondary: '#3498db',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        light: '#ecf0f1'
    },
    
    animations: {
        duration: 1000,
        easing: 'ease-in-out'
    }
};

/**
 * Initialize all charts when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Enhanced Cucumber Charts...');
    
    initializePieCharts();
    initializeBarCharts();
    initializeAnimations();
    initializeTooltips();
    
    console.log('✅ Enhanced Charts initialized successfully!');
});

/**
 * Initialize pie charts with animation
 */
function initializePieCharts() {
    const pieCharts = document.querySelectorAll('.pie-chart');
    
    pieCharts.forEach(chart => {
        const data = JSON.parse(chart.dataset.chartData || '{}');
        animatePieChart(chart, data);
    });
}

/**
 * Animate pie chart creation
 */
function animatePieChart(element, data) {
    const total = Object.values(data).reduce((sum, val) => sum + val, 0);
    let currentAngle = 0;
    
    // Create gradient for pie chart
    const gradients = [];
    Object.keys(data).forEach((key, index) => {
        const percentage = (data[key] / total) * 360;
        const color = getStatusColor(key);
        
        gradients.push(`${color} ${currentAngle}deg ${currentAngle + percentage}deg`);
        currentAngle += percentage;
    });
    
    // Apply animated gradient
    setTimeout(() => {
        element.style.background = `conic-gradient(${gradients.join(', ')})`;
        element.classList.add('fade-in-up');
    }, 100);
    
    // Update center text
    const centerText = element.querySelector('.pie-chart-percentage');
    if (centerText && data.passed) {
        const passRate = Math.round((data.passed / total) * 100);
        animateNumber(centerText, 0, passRate, 1000, '%');
    }
}

/**
 * Initialize bar charts with progressive animation
 */
function initializeBarCharts() {
    const barCharts = document.querySelectorAll('.bar-chart');
    
    barCharts.forEach(chart => {
        const bars = chart.querySelectorAll('.bar-fill');
        
        bars.forEach((bar, index) => {
            const width = bar.dataset.width || '0%';
            
            // Animate bar fill
            setTimeout(() => {
                bar.style.width = width;
                bar.classList.add('fade-in-up');
            }, index * 200);
        });
    });
}

/**
 * Initialize general animations
 */
function initializeAnimations() {
    // Animate stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in-up');
        }, index * 100);
    });
    
    // Animate tables
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        setTimeout(() => {
            table.classList.add('fade-in-up');
        }, 500);
    });
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.style.cssText = `
        position: absolute;
        background: #2c3e50;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.9em;
        pointer-events: none;
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    document.body.appendChild(tooltip);
    
    // Add tooltip functionality to elements
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', (e) => {
            showTooltip(e, element.dataset.tooltip);
        });
        
        element.addEventListener('mouseleave', hideTooltip);
    });
    
    window.showTooltip = function(event, text) {
        tooltip.textContent = text;
        tooltip.style.opacity = '1';
        
        const rect = event.target.getBoundingClientRect();
        tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    };
    
    window.hideTooltip = function() {
        tooltip.style.opacity = '0';
    };
}

/**
 * Animate number counting
 */
function animateNumber(element, start, end, duration, suffix = '') {
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.round(start + (end - start) * easeOutCubic(progress));
        element.textContent = current + suffix;
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

/**
 * Easing function for smooth animations
 */
function easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
}

/**
 * Get color based on status
 */
function getStatusColor(status) {
    const colors = {
        passed: ChartConfig.colors.success,
        failed: ChartConfig.colors.danger,
        skipped: ChartConfig.colors.warning,
        pending: ChartConfig.colors.secondary
    };
    
    return colors[status] || ChartConfig.colors.secondary;
}

/**
 * Utility function to format numbers
 */
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

/**
 * Export functions for external use
 */
window.CucumberCharts = {
    initializePieCharts,
    initializeBarCharts,
    animateNumber,
    formatNumber
};

console.log('📊 Enhanced Cucumber Charts JavaScript loaded successfully!');
