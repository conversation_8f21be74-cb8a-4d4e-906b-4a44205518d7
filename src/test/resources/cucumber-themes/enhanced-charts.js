/**
 * Enhanced Charts JavaScript for Cucumber Reports
 * Provides interactive chart functionality and data visualization
 */

// Chart configuration
const ChartConfig = {
    colors: {
        primary: '#2c3e50',
        secondary: '#3498db',
        success: '#27ae60',
        warning: '#f39c12',
        danger: '#e74c3c',
        light: '#ecf0f1'
    },

    animations: {
        duration: 1000,
        easing: 'ease-in-out'
    }
};

// Global test data
let testData = {
    passed: 0,
    failed: 0,
    skipped: 0,
    total: 0,
    duration: 0,
    features: 0,
    scenarios: 0
};

/**
 * Parse cucumber JSON data and extract statistics
 */
function parseCucumberData() {
    try {
        // Try to fetch cucumber JSON data
        fetch('cucumber-html-reports/cucumber-report.json')
            .then(response => response.json())
            .then(data => {
                testData = extractTestStatistics(data);
                updateAllDisplays();
            })
            .catch(error => {
                console.log('Using sample data - JSON not found:', error.message);
                // Use sample data if JSON not available
                testData = {
                    passed: 2,
                    failed: 1,
                    skipped: 0,
                    total: 3,
                    duration: 6.3,
                    features: 1,
                    scenarios: 3
                };
                updateAllDisplays();
            });
    } catch (error) {
        console.log('Error parsing cucumber data:', error);
        updateAllDisplays();
    }
}

/**
 * Extract test statistics from cucumber JSON
 */
function extractTestStatistics(cucumberData) {
    let stats = {
        passed: 0,
        failed: 0,
        skipped: 0,
        total: 0,
        duration: 0,
        features: cucumberData.length,
        scenarios: 0,
        steps: {
            total: 0,
            passed: 0,
            failed: 0,
            skipped: 0
        },
        featureDetails: [],
        executionTime: new Date().toISOString(),
        environment: 'Test'
    };

    cucumberData.forEach(feature => {
        let featureStats = {
            name: feature.name || 'Unknown Feature',
            id: feature.id || '',
            uri: feature.uri || '',
            description: feature.description || '',
            tags: feature.tags || [],
            scenarios: 0,
            passed: 0,
            failed: 0,
            skipped: 0,
            duration: 0
        };

        if (feature.elements) {
            feature.elements.forEach(scenario => {
                stats.scenarios++;
                featureStats.scenarios++;

                let scenarioPassed = true;
                let scenarioSkipped = false;
                let scenarioDuration = 0;

                if (scenario.steps) {
                    scenario.steps.forEach(step => {
                        stats.steps.total++;

                        if (step.result) {
                            if (step.result.duration) {
                                const stepDuration = step.result.duration / 1000000000; // Convert nanoseconds to seconds
                                stats.duration += stepDuration;
                                scenarioDuration += stepDuration;
                                featureStats.duration += stepDuration;
                            }

                            if (step.result.status === 'passed') {
                                stats.steps.passed++;
                            } else if (step.result.status === 'failed') {
                                stats.steps.failed++;
                                scenarioPassed = false;
                            } else if (step.result.status === 'skipped') {
                                stats.steps.skipped++;
                                scenarioSkipped = true;
                            }
                        }
                    });
                }

                if (!scenarioPassed) {
                    stats.failed++;
                    featureStats.failed++;
                } else if (scenarioSkipped) {
                    stats.skipped++;
                    featureStats.skipped++;
                } else {
                    stats.passed++;
                    featureStats.passed++;
                }

                stats.total++;
            });
        }

        featureStats.duration = Math.round(featureStats.duration * 10) / 10;
        stats.featureDetails.push(featureStats);
    });

    stats.duration = Math.round(stats.duration * 10) / 10; // Round to 1 decimal
    stats.passRate = stats.total > 0 ? Math.round((stats.passed / stats.total) * 100) : 0;
    stats.avgDuration = stats.total > 0 ? Math.round((stats.duration / stats.total) * 100) / 100 : 0;

    return stats;
}

/**
 * Update all displays with current test data
 */
function updateAllDisplays() {
    updateStatistics(testData);
    updateHeaderStats(testData);
    updateDashboardMeta(testData);
    updateTrendIndicators(testData);
    updatePieChart(testData);
    updateFeaturePerformance(testData);
    initializePieCharts();
    initializeBarCharts();
    initializeAnimations();
    initializeTooltips();
}

/**
 * Update dashboard metadata
 */
function updateDashboardMeta(data) {
    const testDateElement = document.getElementById('test-date');
    if (testDateElement) {
        const now = new Date();
        const options = {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        testDateElement.textContent = now.toLocaleDateString('en-US', options);
    }
}

/**
 * Update trend indicators with calculated trends
 */
function updateTrendIndicators(data) {
    // Calculate trends (mock previous run data for demo)
    const previousRun = {
        passed: Math.max(0, data.passed - Math.floor(Math.random() * 3)),
        failed: data.failed + Math.floor(Math.random() * 2),
        total: data.total - Math.floor(Math.random() * 2)
    };

    const trends = {
        passed: calculateTrend(data.passed, previousRun.passed),
        failed: calculateTrend(data.failed, previousRun.failed, true), // Reverse for failed (less is better)
        passRate: calculateTrend(data.passRate, Math.round((previousRun.passed / previousRun.total) * 100)),
        duration: calculateTrend(data.avgDuration, data.avgDuration + 0.5, true) // Reverse for duration (less is better)
    };

    updateTrendElement('.stat-card.success .card-trend', trends.passed, data.passed - previousRun.passed);
    updateTrendElement('.stat-card.failure .card-trend', trends.failed, previousRun.failed - data.failed);
    updateTrendElement('.stat-card.primary .card-trend', trends.passRate, data.passRate - Math.round((previousRun.passed / previousRun.total) * 100));
    updateTrendElement('.stat-card.secondary .card-trend', trends.duration, 0);
}

/**
 * Calculate trend direction
 */
function calculateTrend(current, previous, reverse = false) {
    if (current > previous) {
        return reverse ? 'down' : 'up';
    } else if (current < previous) {
        return reverse ? 'up' : 'down';
    }
    return 'stable';
}

/**
 * Update trend element
 */
function updateTrendElement(selector, trend, value) {
    const element = document.querySelector(selector);
    if (element) {
        element.className = `card-trend ${trend}`;

        let arrow = '→';
        let prefix = '';

        if (trend === 'up') {
            arrow = '↗';
            prefix = '+';
        } else if (trend === 'down') {
            arrow = '↘';
            prefix = '';
        }

        if (value !== 0) {
            element.textContent = `${arrow} ${prefix}${Math.abs(value)}`;
        } else {
            element.textContent = `${arrow} Stable`;
        }
    }
}

/**
 * Update feature performance data
 */
function updateFeaturePerformance(data) {
    if (data.featureDetails && data.featureDetails.length > 0) {
        // This will be used for bar charts showing feature performance
        const featurePerformanceData = data.featureDetails.map(feature => ({
            name: feature.name,
            passRate: feature.scenarios > 0 ? Math.round((feature.passed / feature.scenarios) * 100) : 0,
            duration: feature.duration,
            scenarios: feature.scenarios
        }));

        // Store for use in charts
        window.featurePerformanceData = featurePerformanceData;
    }
}

/**
 * Initialize all charts when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Initializing Enhanced Cucumber Charts...');

    // Parse cucumber data first
    parseCucumberData();

    console.log('✅ Enhanced Charts initialized successfully!');
});

/**
 * Update pie chart with real data
 */
function updatePieChart(data) {
    const pieChart = document.querySelector('.pie-chart');
    if (pieChart) {
        pieChart.dataset.chartData = JSON.stringify({
            passed: data.passed,
            failed: data.failed,
            skipped: data.skipped
        });

        // Update legend values
        const legendItems = document.querySelectorAll('.legend-value');
        if (legendItems.length >= 3) {
            legendItems[0].textContent = data.passed;
            legendItems[1].textContent = data.failed;
            legendItems[2].textContent = data.skipped;
        }
    }
}

/**
 * Initialize pie charts with animation
 */
function initializePieCharts() {
    const pieCharts = document.querySelectorAll('.pie-chart');

    pieCharts.forEach(chart => {
        const data = JSON.parse(chart.dataset.chartData || '{}');
        animatePieChart(chart, data);
    });
}

/**
 * Animate pie chart creation
 */
function animatePieChart(element, data) {
    const total = Object.values(data).reduce((sum, val) => sum + val, 0);
    let currentAngle = 0;
    
    // Create gradient for pie chart
    const gradients = [];
    Object.keys(data).forEach((key, index) => {
        const percentage = (data[key] / total) * 360;
        const color = getStatusColor(key);
        
        gradients.push(`${color} ${currentAngle}deg ${currentAngle + percentage}deg`);
        currentAngle += percentage;
    });
    
    // Apply animated gradient
    setTimeout(() => {
        element.style.background = `conic-gradient(${gradients.join(', ')})`;
        element.classList.add('fade-in-up');
    }, 100);
    
    // Update center text
    const centerText = element.querySelector('.pie-chart-percentage');
    if (centerText && data.passed) {
        const passRate = Math.round((data.passed / total) * 100);
        animateNumber(centerText, 0, passRate, 1000, '%');
    }
}

/**
 * Initialize bar charts with progressive animation
 */
function initializeBarCharts() {
    const barCharts = document.querySelectorAll('.bar-chart');
    
    barCharts.forEach(chart => {
        const bars = chart.querySelectorAll('.bar-fill');
        
        bars.forEach((bar, index) => {
            const width = bar.dataset.width || '0%';
            
            // Animate bar fill
            setTimeout(() => {
                bar.style.width = width;
                bar.classList.add('fade-in-up');
            }, index * 200);
        });
    });
}

/**
 * Initialize general animations
 */
function initializeAnimations() {
    // Animate stat cards
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in-up');
        }, index * 100);
    });
    
    // Animate tables
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        setTimeout(() => {
            table.classList.add('fade-in-up');
        }, 500);
    });
}

/**
 * Initialize tooltips
 */
function initializeTooltips() {
    // Create tooltip element
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.style.cssText = `
        position: absolute;
        background: #2c3e50;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.9em;
        pointer-events: none;
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s ease;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;
    document.body.appendChild(tooltip);
    
    // Add tooltip functionality to elements
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    tooltipElements.forEach(element => {
        element.addEventListener('mouseenter', (e) => {
            showTooltip(e, element.dataset.tooltip);
        });
        
        element.addEventListener('mouseleave', hideTooltip);
    });
    
    window.showTooltip = function(event, text) {
        tooltip.textContent = text;
        tooltip.style.opacity = '1';
        
        const rect = event.target.getBoundingClientRect();
        tooltip.style.left = rect.left + rect.width / 2 - tooltip.offsetWidth / 2 + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
    };
    
    window.hideTooltip = function() {
        tooltip.style.opacity = '0';
    };
}

/**
 * Animate number counting
 */
function animateNumber(element, start, end, duration, suffix = '') {
    const startTime = performance.now();
    
    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.round(start + (end - start) * easeOutCubic(progress));
        element.textContent = current + suffix;
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    requestAnimationFrame(updateNumber);
}

/**
 * Easing function for smooth animations
 */
function easeOutCubic(t) {
    return 1 - Math.pow(1 - t, 3);
}

/**
 * Get color based on status
 */
function getStatusColor(status) {
    const colors = {
        passed: ChartConfig.colors.success,
        failed: ChartConfig.colors.danger,
        skipped: ChartConfig.colors.warning,
        pending: ChartConfig.colors.secondary
    };
    
    return colors[status] || ChartConfig.colors.secondary;
}

/**
 * Utility function to format numbers
 */
function formatNumber(num) {
    if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
}

/**
 * Export functions for external use
 */
window.CucumberCharts = {
    initializePieCharts,
    initializeBarCharts,
    animateNumber,
    formatNumber
};

console.log('📊 Enhanced Cucumber Charts JavaScript loaded successfully!');
