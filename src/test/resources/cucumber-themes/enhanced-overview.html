<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🥒 Cucumber Test Report</title>
    <link rel="stylesheet" href="cucumber-html-reports/css/enhanced-theme.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🥒</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- Top Header -->
        <header class="top-header">
            <div class="header-left">
                <div class="logo">🥒</div>
                <div class="header-title">
                    <h1>Cucumber Test Report</h1>
                    <span class="header-subtitle">Enhanced Visual Analytics Dashboard</span>
                </div>
            </div>
            <div class="header-right">
                <div class="header-metrics">
                    <div class="metric">
                        <span class="metric-value" id="top-pass-rate">-%</span>
                        <span class="metric-label">PASS RATE</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="top-total">-</span>
                        <span class="metric-label">TOTAL</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="top-duration">-s</span>
                        <span class="metric-label">DURATION</span>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn-action">📊 Export</button>
                    <button class="btn-action primary">🔄 Refresh</button>
                </div>
            </div>
        </header>

        <div class="main-layout">
            <!-- Sidebar Navigation -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3>Navigation</h3>
                    <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
                </div>

                <nav class="sidebar-nav">
                    <a href="#overview" class="nav-item active" data-tab="overview">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">Overview</span>
                    </a>
                    <a href="#features" class="nav-item" data-tab="features">
                        <span class="nav-icon">📋</span>
                        <span class="nav-text">Features</span>
                    </a>
                    <a href="#scenarios" class="nav-item" data-tab="scenarios">
                        <span class="nav-icon">🎯</span>
                        <span class="nav-text">Scenarios</span>
                    </a>
                    <a href="#steps" class="nav-item" data-tab="steps">
                        <span class="nav-icon">👣</span>
                        <span class="nav-text">Steps</span>
                    </a>
                    <a href="#analytics" class="nav-item" data-tab="analytics">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">Analytics</span>
                    </a>
                    <a href="#trends" class="nav-item" data-tab="trends">
                        <span class="nav-icon">📉</span>
                        <span class="nav-text">Trends</span>
                    </a>
                </nav>
            </aside>

        <!-- Navigation Tabs -->
        <nav>
            <ul class="nav-tabs">
                <li><a href="#overview" class="active">📊 Overview</a></li>
                <li><a href="#features">📋 Features</a></li>
                <li><a href="#scenarios">🎯 Scenarios</a></li>
                <li><a href="#steps">👣 Steps</a></li>
            </ul>
        </nav>

        <!-- Statistics Dashboard -->
        <section class="stats-dashboard">
            <div class="dashboard-header">
                <h2 class="dashboard-title">📊 Test Results Overview</h2>
                <div class="dashboard-meta">
                    <span class="meta-item">🕒 Last updated: <strong id="last-updated">Loading...</strong></span>
                    <span class="meta-item">🌍 Environment: <strong id="environment">Loading...</strong></span>
                    <span class="meta-item">📅 <span id="test-date">Loading...</span></span>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card success">
                    <div class="card-header">
                        <div class="card-icon">✅</div>
                        <div class="card-trend">Loading...</div>
                    </div>
                    <div class="card-content">
                        <div class="stat-number" id="passed-count">-</div>
                        <div class="stat-label">Passed Tests</div>
                        <div class="stat-description">Successfully executed</div>
                    </div>
                </div>

                <div class="stat-card failure">
                    <div class="card-header">
                        <div class="card-icon">❌</div>
                        <div class="card-trend">Loading...</div>
                    </div>
                    <div class="card-content">
                        <div class="stat-number" id="failed-count">-</div>
                        <div class="stat-label">Failed Tests</div>
                        <div class="stat-description">Need attention</div>
                    </div>
                </div>

                <div class="stat-card warning">
                    <div class="card-header">
                        <div class="card-icon">⏭️</div>
                        <div class="card-trend">Loading...</div>
                    </div>
                    <div class="card-content">
                        <div class="stat-number" id="skipped-count">-</div>
                        <div class="stat-label">Skipped Tests</div>
                        <div class="stat-description">Not executed</div>
                    </div>
                </div>

                <div class="stat-card info">
                    <div class="card-header">
                        <div class="card-icon">📊</div>
                        <div class="card-trend">Loading...</div>
                    </div>
                    <div class="card-content">
                        <div class="stat-number" id="total-count">-</div>
                        <div class="stat-label">Total Tests</div>
                        <div class="stat-description">All scenarios</div>
                    </div>
                </div>

                <div class="stat-card primary">
                    <div class="card-header">
                        <div class="card-icon">🎯</div>
                        <div class="card-trend">Loading...</div>
                    </div>
                    <div class="card-content">
                        <div class="stat-number" id="pass-rate">-%</div>
                        <div class="stat-label">Success Rate</div>
                        <div class="stat-description">Quality metric</div>
                    </div>
                </div>

                <div class="stat-card secondary">
                    <div class="card-header">
                        <div class="card-icon">⏱️</div>
                        <div class="card-trend">Loading...</div>
                    </div>
                    <div class="card-content">
                        <div class="stat-number" id="duration">-s</div>
                        <div class="stat-label">Total Duration</div>
                        <div class="stat-description">Execution time</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Content Area -->
        <main class="content">
            <!-- Overview Tab -->
            <section id="overview" class="tab-content active">
                <!-- Pie Chart Section -->
                <div class="chart-container">
                    <h2 class="chart-title">📊 Test Results Distribution</h2>
                    <div class="pie-chart-container">
                        <div class="pie-chart" data-chart-data='{}'>
                            <div class="pie-chart-center">
                                <div class="pie-chart-percentage">-%</div>
                                <div class="pie-chart-label">Success Rate</div>
                            </div>
                        </div>
                        <div class="pie-legend">
                            <div class="legend-item">
                                <div class="legend-color passed"></div>
                                <div class="legend-text">
                                    <span class="legend-label">Passed</span>
                                    <span class="legend-value">-</span>
                                </div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color failed"></div>
                                <div class="legend-text">
                                    <span class="legend-label">Failed</span>
                                    <span class="legend-value">-</span>
                                </div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color skipped"></div>
                                <div class="legend-text">
                                    <span class="legend-label">Skipped</span>
                                    <span class="legend-value">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Information -->
                <div class="chart-container">
                    <h2 class="chart-title">🎯 Enhanced Features</h2>
                    <div style="padding: 20px; text-align: center;">
                        <p style="font-size: 1.2em; color: #2c3e50; margin-bottom: 20px;">
                            This enhanced report includes interactive charts, responsive design, and professional styling.
                        </p>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                            <div style="background: #ecf0f1; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                                <h3 style="margin: 0 0 10px 0; color: #3498db;">📊 Interactive Charts</h3>
                                <p style="margin: 0; color: #7f8c8d;">Dynamic pie charts with hover effects</p>
                            </div>
                            <div style="background: #ecf0f1; padding: 20px; border-radius: 8px; border-left: 4px solid #27ae60;">
                                <h3 style="margin: 0 0 10px 0; color: #27ae60;">📱 Responsive Design</h3>
                                <p style="margin: 0; color: #7f8c8d;">Works perfectly on all devices</p>
                            </div>
                            <div style="background: #ecf0f1; padding: 20px; border-radius: 8px; border-left: 4px solid #f39c12;">
                                <h3 style="margin: 0 0 10px 0; color: #f39c12;">🎨 Professional Styling</h3>
                                <p style="margin: 0; color: #7f8c8d;">Modern and clean interface</p>
                            </div>
                        </div>
                        
                        <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; color: white;">
                            <h3 style="margin: 0 0 10px 0;">🚀 Quick Actions</h3>
                            <p style="margin: 0 0 15px 0;">Access standard reports for detailed information:</p>
                            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                                <a href="cucumber-html-reports/overview-features.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; transition: all 0.3s ease;">📋 Features</a>
                                <a href="cucumber-html-reports/overview-steps.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; transition: all 0.3s ease;">👣 Steps</a>
                                <a href="cucumber-html-reports/overview-tags.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; transition: all 0.3s ease;">🏷️ Tags</a>
                                <a href="cucumber-html-reports/overview-failures.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; transition: all 0.3s ease;">❌ Failures</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Other tabs would be here -->
            <section id="features" class="tab-content">
                <div class="chart-container">
                    <h2 class="chart-title">📋 Features Overview</h2>
                    <p style="text-align: center; color: #7f8c8d;">
                        <a href="cucumber-html-reports/overview-features.html" style="color: #3498db; text-decoration: none; font-size: 1.1em;">
                            → View Detailed Features Report
                        </a>
                    </p>
                </div>
            </section>

            <section id="scenarios" class="tab-content">
                <div class="chart-container">
                    <h2 class="chart-title">🎯 Scenarios Overview</h2>
                    <p style="text-align: center; color: #7f8c8d;">
                        Scenario details are available in the features report.
                    </p>
                </div>
            </section>

            <section id="steps" class="tab-content">
                <div class="chart-container">
                    <h2 class="chart-title">👣 Steps Overview</h2>
                    <p style="text-align: center; color: #7f8c8d;">
                        <a href="cucumber-html-reports/overview-steps.html" style="color: #3498db; text-decoration: none; font-size: 1.1em;">
                            → View Detailed Steps Report
                        </a>
                    </p>
                </div>
            </section>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="cucumber-html-reports/js/enhanced-charts.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeTabNavigation();
            updateTestDate();
        });

        function updateStatistics(data) {
            document.getElementById('passed-count').textContent = data.passed;
            document.getElementById('failed-count').textContent = data.failed;
            document.getElementById('skipped-count').textContent = data.skipped;
            document.getElementById('total-count').textContent = data.total;
            document.getElementById('pass-rate').textContent = Math.round((data.passed / data.total) * 100) + '%';
            document.getElementById('duration').textContent = data.duration + 's';
        }

        function updateHeaderStats(data) {
            document.getElementById('header-passed').textContent = data.passed;
            document.getElementById('header-failed').textContent = data.failed;
            document.getElementById('header-total').textContent = data.total;
            document.getElementById('header-pass-rate').textContent = Math.round((data.passed / data.total) * 100) + '%';
        }

        function updateQuickStats(data) {
            // This function will be called by enhanced-charts.js
        }

        function updateDashboardMeta(data) {
            // Update last updated time
            const lastUpdatedElement = document.getElementById('last-updated');
            if (lastUpdatedElement) {
                lastUpdatedElement.textContent = 'Just now';
            }

            // Update environment
            const environmentElement = document.getElementById('environment');
            if (environmentElement) {
                environmentElement.textContent = data.environment || 'Test';
            }

            updateTestDate();
        }

        function updateTestDate() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            const testDateElement = document.getElementById('test-date');
            if (testDateElement) {
                testDateElement.textContent = now.toLocaleDateString('en-US', options);
            }
        }

        function initializeTabNavigation() {
            const tabs = document.querySelectorAll('.nav-tabs a');
            const contents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));
                    
                    // Add active class to clicked tab
                    this.classList.add('active');
                    
                    // Show corresponding content
                    const targetId = this.getAttribute('href').substring(1);
                    const targetContent = document.getElementById(targetId);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });
        }
    </script>
</body>
</html>
