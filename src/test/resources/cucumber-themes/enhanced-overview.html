<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🥒 Cucumber Test Report</title>
    <link rel="stylesheet" href="cucumber-html-reports/css/enhanced-theme.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🥒</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- Top Header -->
        <header class="top-header">
            <div class="header-left">
                <div class="logo">🥒</div>
                <div class="header-title">
                    <h1>Cucumber Test Report</h1>
                    <span class="header-subtitle">Enhanced Visual Analytics Dashboard</span>
                </div>
            </div>
            <div class="header-right">
                <div class="header-metrics">
                    <div class="metric">
                        <span class="metric-value" id="top-pass-rate">-%</span>
                        <span class="metric-label">PASS RATE</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="top-total">-</span>
                        <span class="metric-label">TOTAL</span>
                    </div>
                    <div class="metric">
                        <span class="metric-value" id="top-duration">-s</span>
                        <span class="metric-label">DURATION</span>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="btn-action">📊 Export</button>
                    <button class="btn-action primary">🔄 Refresh</button>
                </div>
            </div>
        </header>

        <div class="main-layout">
            <!-- Sidebar Navigation -->
            <aside class="sidebar">
                <div class="sidebar-header">
                    <h3>Navigation</h3>
                    <button class="sidebar-toggle" onclick="toggleSidebar()">☰</button>
                </div>

                <nav class="sidebar-nav">
                    <a href="#overview" class="nav-item active" data-tab="overview">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">Overview</span>
                    </a>
                    <a href="#features" class="nav-item" data-tab="features">
                        <span class="nav-icon">📋</span>
                        <span class="nav-text">Features</span>
                    </a>
                    <a href="#scenarios" class="nav-item" data-tab="scenarios">
                        <span class="nav-icon">🎯</span>
                        <span class="nav-text">Scenarios</span>
                    </a>
                    <a href="#steps" class="nav-item" data-tab="steps">
                        <span class="nav-icon">👣</span>
                        <span class="nav-text">Steps</span>
                    </a>
                    <a href="#analytics" class="nav-item" data-tab="analytics">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">Analytics</span>
                    </a>
                    <a href="#trends" class="nav-item" data-tab="trends">
                        <span class="nav-icon">📉</span>
                        <span class="nav-text">Trends</span>
                    </a>
                </nav>
            </aside>

            <!-- Main Content -->
            <main class="main-content">
                <!-- Overview Tab -->
                <div id="overview" class="tab-content active">
                    <!-- Top Stats Cards -->
                    <div class="stats-overview">
                        <div class="stat-card passed">
                            <div class="stat-icon">✅</div>
                            <div class="stat-content">
                                <div class="stat-number" id="passed-count">-</div>
                                <div class="stat-label">PASSED</div>
                                <div class="stat-trend" id="passed-trend">-% from last run</div>
                            </div>
                        </div>

                        <div class="stat-card failed">
                            <div class="stat-icon">❌</div>
                            <div class="stat-content">
                                <div class="stat-number" id="failed-count">-</div>
                                <div class="stat-label">FAILED</div>
                                <div class="stat-trend" id="failed-trend">-% from last run</div>
                            </div>
                        </div>

                        <div class="stat-card skipped">
                            <div class="stat-icon">⏭️</div>
                            <div class="stat-content">
                                <div class="stat-number" id="skipped-count">-</div>
                                <div class="stat-label">SKIPPED</div>
                                <div class="stat-trend" id="skipped-trend">Same as last run</div>
                            </div>
                        </div>

                        <div class="stat-card features">
                            <div class="stat-icon">📋</div>
                            <div class="stat-content">
                                <div class="stat-number" id="features-count">-</div>
                                <div class="stat-label">FEATURES</div>
                                <div class="stat-trend" id="features-trend">Total features</div>
                            </div>
                        </div>

                        <div class="stat-card scenarios">
                            <div class="stat-icon">🎯</div>
                            <div class="stat-content">
                                <div class="stat-number" id="scenarios-count">-</div>
                                <div class="stat-label">SCENARIOS</div>
                                <div class="stat-trend" id="scenarios-trend">Total scenarios</div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Results Overview Section -->
                    <div class="overview-section">
                        <div class="section-header">
                            <h2 class="section-title">📊 Test Results Overview</h2>
                            <div class="section-meta">
                                <span class="meta-item">Last updated: <strong id="last-updated">Just now</strong></span>
                                <span class="meta-item">Environment: <strong id="environment">Test</strong></span>
                            </div>
                        </div>

                        <div class="overview-grid">
                            <!-- Test Results Distribution -->
                            <div class="overview-card">
                                <div class="card-header">
                                    <h3 class="card-title">📊 Test Results Distribution</h3>
                                </div>
                                <div class="card-content">
                                    <div class="pie-chart-container">
                                        <div class="pie-chart" data-chart-data='{}'>
                                            <div class="pie-chart-center">
                                                <div class="pie-chart-percentage">-%</div>
                                                <div class="pie-chart-label">Success Rate</div>
                                            </div>
                                        </div>
                                        <div class="pie-legend">
                                            <div class="legend-item">
                                                <div class="legend-color passed"></div>
                                                <div class="legend-text">
                                                    <span class="legend-label">Passed</span>
                                                    <span class="legend-value">-</span>
                                                </div>
                                            </div>
                                            <div class="legend-item">
                                                <div class="legend-color failed"></div>
                                                <div class="legend-text">
                                                    <span class="legend-label">Failed</span>
                                                    <span class="legend-value">-</span>
                                                </div>
                                            </div>
                                            <div class="legend-item">
                                                <div class="legend-color skipped"></div>
                                                <div class="legend-text">
                                                    <span class="legend-label">Skipped</span>
                                                    <span class="legend-value">-</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Performance Metrics -->
                            <div class="overview-card">
                                <div class="card-header">
                                    <h3 class="card-title">🎯 Performance Metrics</h3>
                                </div>
                                <div class="card-content">
                                    <div class="performance-metrics">
                                        <div class="metric-gauge">
                                            <div class="gauge-container">
                                                <div class="gauge" id="success-gauge">
                                                    <div class="gauge-fill"></div>
                                                    <div class="gauge-center">
                                                        <span class="gauge-value">-%</span>
                                                        <span class="gauge-label">Success Rate</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="metric-gauge">
                                            <div class="gauge-container">
                                                <div class="gauge" id="coverage-gauge">
                                                    <div class="gauge-fill"></div>
                                                    <div class="gauge-center">
                                                        <span class="gauge-value">-%</span>
                                                        <span class="gauge-label">Coverage</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features Tab -->
                <div id="features" class="tab-content">
                    <div class="overview-section">
                        <div class="section-header">
                            <h2 class="section-title">📋 Features Overview</h2>
                            <div class="section-meta">
                                <span class="meta-item">Total: <strong id="features-total">-</strong></span>
                                <span class="meta-item">Passed: <strong id="features-passed">-</strong></span>
                                <span class="meta-item">Failed: <strong id="features-failed">-</strong></span>
                            </div>
                        </div>

                        <!-- Features Summary Cards -->
                        <div class="features-summary">
                            <div class="summary-card">
                                <div class="summary-icon">📊</div>
                                <div class="summary-content">
                                    <div class="summary-number" id="avg-scenarios">-</div>
                                    <div class="summary-label">Avg Scenarios per Feature</div>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">⏱️</div>
                                <div class="summary-content">
                                    <div class="summary-number" id="avg-duration">-s</div>
                                    <div class="summary-label">Avg Duration per Feature</div>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">🎯</div>
                                <div class="summary-content">
                                    <div class="summary-number" id="feature-success-rate">-%</div>
                                    <div class="summary-label">Feature Success Rate</div>
                                </div>
                            </div>
                        </div>

                        <!-- Features Table -->
                        <div class="features-table-container">
                            <div class="table-header">
                                <h3>Feature Details</h3>
                                <div class="table-filters">
                                    <select id="feature-filter" class="filter-select">
                                        <option value="all">All Features</option>
                                        <option value="passed">Passed Only</option>
                                        <option value="failed">Failed Only</option>
                                    </select>
                                </div>
                            </div>
                            <div class="enhanced-table">
                                <table class="features-table">
                                    <thead>
                                        <tr>
                                            <th>Feature Name</th>
                                            <th>Scenarios</th>
                                            <th>Passed</th>
                                            <th>Failed</th>
                                            <th>Skipped</th>
                                            <th>Duration</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody id="features-table-body">
                                        <tr>
                                            <td colspan="7" class="loading-row">Loading features data...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Scenarios Tab -->
                <div id="scenarios" class="tab-content">
                    <div class="overview-section">
                        <div class="section-header">
                            <h2 class="section-title">🎯 Scenarios Overview</h2>
                            <div class="section-meta">
                                <span class="meta-item">Total: <strong id="scenarios-total">-</strong></span>
                                <span class="meta-item">Avg Steps: <strong id="avg-steps">-</strong></span>
                            </div>
                        </div>

                        <!-- Scenarios Chart -->
                        <div class="overview-grid">
                            <div class="overview-card">
                                <div class="card-header">
                                    <h3 class="card-title">📊 Scenario Status Distribution</h3>
                                </div>
                                <div class="card-content">
                                    <div class="scenario-chart">
                                        <canvas id="scenario-bar-chart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>

                            <div class="overview-card">
                                <div class="card-header">
                                    <h3 class="card-title">⏱️ Execution Time Analysis</h3>
                                </div>
                                <div class="card-content">
                                    <div class="time-analysis">
                                        <div class="time-metric">
                                            <span class="time-label">Fastest Scenario</span>
                                            <span class="time-value" id="fastest-scenario">-s</span>
                                        </div>
                                        <div class="time-metric">
                                            <span class="time-label">Slowest Scenario</span>
                                            <span class="time-value" id="slowest-scenario">-s</span>
                                        </div>
                                        <div class="time-metric">
                                            <span class="time-label">Average Duration</span>
                                            <span class="time-value" id="avg-scenario-duration">-s</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Scenarios Table -->
                        <div class="scenarios-table-container">
                            <div class="table-header">
                                <h3>Scenario Details</h3>
                                <div class="table-filters">
                                    <select id="scenario-filter" class="filter-select">
                                        <option value="all">All Scenarios</option>
                                        <option value="passed">Passed Only</option>
                                        <option value="failed">Failed Only</option>
                                        <option value="skipped">Skipped Only</option>
                                    </select>
                                    <input type="text" id="scenario-search" class="search-input" placeholder="Search scenarios...">
                                </div>
                            </div>
                            <div class="enhanced-table">
                                <table class="scenarios-table">
                                    <thead>
                                        <tr>
                                            <th>Scenario Name</th>
                                            <th>Feature</th>
                                            <th>Steps</th>
                                            <th>Duration</th>
                                            <th>Status</th>
                                            <th>Tags</th>
                                        </tr>
                                    </thead>
                                    <tbody id="scenarios-table-body">
                                        <tr>
                                            <td colspan="6" class="loading-row">Loading scenarios data...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Steps Tab -->
                <div id="steps" class="tab-content">
                    <div class="overview-section">
                        <div class="section-header">
                            <h2 class="section-title">👣 Steps Overview</h2>
                            <div class="section-meta">
                                <span class="meta-item">Total Steps: <strong id="steps-total">-</strong></span>
                                <span class="meta-item">Unique Steps: <strong id="unique-steps">-</strong></span>
                            </div>
                        </div>

                        <!-- Steps Summary -->
                        <div class="steps-summary">
                            <div class="summary-card passed">
                                <div class="summary-icon">✅</div>
                                <div class="summary-content">
                                    <div class="summary-number" id="steps-passed">-</div>
                                    <div class="summary-label">Passed Steps</div>
                                </div>
                            </div>
                            <div class="summary-card failed">
                                <div class="summary-icon">❌</div>
                                <div class="summary-content">
                                    <div class="summary-number" id="steps-failed">-</div>
                                    <div class="summary-label">Failed Steps</div>
                                </div>
                            </div>
                            <div class="summary-card skipped">
                                <div class="summary-icon">⏭️</div>
                                <div class="summary-content">
                                    <div class="summary-number" id="steps-skipped">-</div>
                                    <div class="summary-label">Skipped Steps</div>
                                </div>
                            </div>
                            <div class="summary-card">
                                <div class="summary-icon">⚡</div>
                                <div class="summary-content">
                                    <div class="summary-number" id="avg-step-duration">-ms</div>
                                    <div class="summary-label">Avg Step Duration</div>
                                </div>
                            </div>
                        </div>

                        <!-- Step Types Analysis -->
                        <div class="overview-grid">
                            <div class="overview-card">
                                <div class="card-header">
                                    <h3 class="card-title">📊 Step Types Distribution</h3>
                                </div>
                                <div class="card-content">
                                    <div class="step-types">
                                        <div class="step-type-item">
                                            <span class="step-type-label">Given</span>
                                            <div class="step-type-bar">
                                                <div class="step-type-fill given" style="width: 0%"></div>
                                            </div>
                                            <span class="step-type-count" id="given-count">-</span>
                                        </div>
                                        <div class="step-type-item">
                                            <span class="step-type-label">When</span>
                                            <div class="step-type-bar">
                                                <div class="step-type-fill when" style="width: 0%"></div>
                                            </div>
                                            <span class="step-type-count" id="when-count">-</span>
                                        </div>
                                        <div class="step-type-item">
                                            <span class="step-type-label">Then</span>
                                            <div class="step-type-bar">
                                                <div class="step-type-fill then" style="width: 0%"></div>
                                            </div>
                                            <span class="step-type-count" id="then-count">-</span>
                                        </div>
                                        <div class="step-type-item">
                                            <span class="step-type-label">And</span>
                                            <div class="step-type-bar">
                                                <div class="step-type-fill and" style="width: 0%"></div>
                                            </div>
                                            <span class="step-type-count" id="and-count">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="overview-card">
                                <div class="card-header">
                                    <h3 class="card-title">🐌 Slowest Steps</h3>
                                </div>
                                <div class="card-content">
                                    <div class="slowest-steps" id="slowest-steps-list">
                                        <div class="loading-text">Loading step performance data...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="analytics" class="tab-content">
                    <div class="overview-section">
                        <div class="section-header">
                            <h2 class="section-title">📈 Analytics & Insights</h2>
                            <div class="section-meta">
                                <span class="meta-item">Analysis Date: <strong id="analysis-date">-</strong></span>
                            </div>
                        </div>

                        <!-- Key Metrics -->
                        <div class="analytics-metrics">
                            <div class="metric-card">
                                <div class="metric-icon">🎯</div>
                                <div class="metric-content">
                                    <div class="metric-value" id="quality-score">-%</div>
                                    <div class="metric-label">Quality Score</div>
                                    <div class="metric-description">Based on pass rate and coverage</div>
                                </div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-icon">⚡</div>
                                <div class="metric-content">
                                    <div class="metric-value" id="performance-score">-%</div>
                                    <div class="metric-label">Performance Score</div>
                                    <div class="metric-description">Based on execution time</div>
                                </div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-icon">🔍</div>
                                <div class="metric-content">
                                    <div class="metric-value" id="coverage-score">-%</div>
                                    <div class="metric-label">Coverage Score</div>
                                    <div class="metric-description">Feature and scenario coverage</div>
                                </div>
                            </div>
                        </div>

                        <!-- Analytics Charts -->
                        <div class="overview-grid">
                            <div class="overview-card">
                                <div class="card-header">
                                    <h3 class="card-title">📊 Test Execution Timeline</h3>
                                </div>
                                <div class="card-content">
                                    <div class="timeline-chart">
                                        <canvas id="timeline-chart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>

                            <div class="overview-card">
                                <div class="card-header">
                                    <h3 class="card-title">🏆 Top Performing Features</h3>
                                </div>
                                <div class="card-content">
                                    <div class="top-features" id="top-features-list">
                                        <div class="loading-text">Analyzing feature performance...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Insights & Recommendations -->
                        <div class="insights-section">
                            <div class="insights-header">
                                <h3>💡 Insights & Recommendations</h3>
                            </div>
                            <div class="insights-grid" id="insights-container">
                                <div class="insight-card">
                                    <div class="insight-icon">🔍</div>
                                    <div class="insight-content">
                                        <h4>Analyzing test results...</h4>
                                        <p>Please wait while we generate insights from your test data.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Trends Tab -->
                <div id="trends" class="tab-content">
                    <div class="overview-section">
                        <div class="section-header">
                            <h2 class="section-title">📉 Trends</h2>
                        </div>
                        <div class="redirect-card">
                            <div class="redirect-content">
                                <h3>Trend Analysis</h3>
                                <p>Coming soon - Historical trend analysis and reporting.</p>
                                <button class="btn-redirect disabled">
                                    📉 Coming Soon
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="cucumber-html-reports/js/enhanced-charts.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeTabNavigation();
            updateTestDate();
        });

        function updateStatistics(data) {
            // Update main stat cards
            document.getElementById('passed-count').textContent = data.passed;
            document.getElementById('failed-count').textContent = data.failed;
            document.getElementById('skipped-count').textContent = data.skipped;
            document.getElementById('features-count').textContent = data.features || 0;
            document.getElementById('scenarios-count').textContent = data.scenarios || data.total;

            // Update top header metrics
            updateTopMetrics(data);

            // Update pie chart percentage
            const piePercentage = document.querySelector('.pie-chart-percentage');
            if (piePercentage) {
                piePercentage.textContent = Math.round((data.passed / data.total) * 100) + '%';
            }

            // Update gauge values
            const successGauge = document.querySelector('#success-gauge .gauge-value');
            if (successGauge) {
                successGauge.textContent = Math.round((data.passed / data.total) * 100) + '%';
            }

            const coverageGauge = document.querySelector('#coverage-gauge .gauge-value');
            if (coverageGauge) {
                coverageGauge.textContent = '0%'; // Placeholder for coverage
            }
        }

        function updateHeaderStats(data) {
            // This function is now handled by updateTopMetrics
            updateTopMetrics(data);
        }

        function updateQuickStats(data) {
            // This function will be called by enhanced-charts.js
        }

        function updateDashboardMeta(data) {
            // Update last updated time
            const lastUpdatedElement = document.getElementById('last-updated');
            if (lastUpdatedElement) {
                lastUpdatedElement.textContent = 'Just now';
            }

            // Update environment
            const environmentElement = document.getElementById('environment');
            if (environmentElement) {
                environmentElement.textContent = data.environment || 'Test';
            }

            updateTestDate();
        }

        function updateTestDate() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            const testDateElement = document.getElementById('test-date');
            if (testDateElement) {
                testDateElement.textContent = now.toLocaleDateString('en-US', options);
            }
        }

        function initializeTabNavigation() {
            const navItems = document.querySelectorAll('.nav-item');
            const tabContents = document.querySelectorAll('.tab-content');

            navItems.forEach(navItem => {
                navItem.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all nav items and tab contents
                    navItems.forEach(item => item.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked nav item
                    this.classList.add('active');

                    // Show corresponding tab content
                    const targetTab = this.getAttribute('data-tab');
                    const targetContent = document.getElementById(targetTab);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });
        }

        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('collapsed');
        }

        function updateTopMetrics(data) {
            document.getElementById('top-pass-rate').textContent = Math.round((data.passed / data.total) * 100) + '%';
            document.getElementById('top-total').textContent = data.total;
            document.getElementById('top-duration').textContent = data.duration + 's';
        }
    </script>
</body>
</html>
