<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🥒 Enhanced Cucumber Test Report</title>
    <link rel="stylesheet" href="css/enhanced-theme.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🥒</text></svg>">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <h1>Enhanced Cucumber Test Report</h1>
            <div class="subtitle">Professional Test Results Dashboard</div>
        </header>

        <!-- Navigation Tabs -->
        <nav>
            <ul class="nav-tabs">
                <li><a href="#overview" class="active">📊 Overview</a></li>
                <li><a href="#features">📋 Features</a></li>
                <li><a href="#scenarios">🎯 Scenarios</a></li>
                <li><a href="#steps">👣 Steps</a></li>
            </ul>
        </nav>

        <!-- Statistics Cards -->
        <section class="stats-container">
            <div class="stat-card success">
                <div class="stat-number" id="passed-count">0</div>
                <div class="stat-label">Passed</div>
            </div>
            <div class="stat-card failure">
                <div class="stat-number" id="failed-count">0</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-number" id="skipped-count">0</div>
                <div class="stat-label">Skipped</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-count">0</div>
                <div class="stat-label">Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pass-rate">0%</div>
                <div class="stat-label">Pass Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="duration">0s</div>
                <div class="stat-label">Duration</div>
            </div>
        </section>

        <!-- Main Content Area -->
        <main class="content">
            <!-- Overview Tab -->
            <section id="overview" class="tab-content active">
                <!-- Pie Chart Section -->
                <div class="chart-container">
                    <h2 class="chart-title">📊 Test Results Distribution</h2>
                    <div class="pie-chart-container">
                        <div class="pie-chart" data-chart-data='{"passed":2,"failed":1,"skipped":0}'>
                            <div class="pie-chart-center">
                                <div class="pie-chart-percentage">0%</div>
                                <div class="pie-chart-label">Success Rate</div>
                            </div>
                        </div>
                        <div class="pie-legend">
                            <div class="legend-item">
                                <div class="legend-color passed"></div>
                                <div class="legend-text">
                                    <span class="legend-label">Passed</span>
                                    <span class="legend-value">2</span>
                                </div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color failed"></div>
                                <div class="legend-text">
                                    <span class="legend-label">Failed</span>
                                    <span class="legend-value">1</span>
                                </div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color skipped"></div>
                                <div class="legend-text">
                                    <span class="legend-label">Skipped</span>
                                    <span class="legend-value">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Information -->
                <div class="chart-container">
                    <h2 class="chart-title">🎯 Enhanced Features</h2>
                    <div style="padding: 20px; text-align: center;">
                        <p style="font-size: 1.2em; color: #2c3e50; margin-bottom: 20px;">
                            This enhanced report includes interactive charts, responsive design, and professional styling.
                        </p>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0;">
                            <div style="background: #ecf0f1; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                                <h3 style="margin: 0 0 10px 0; color: #3498db;">📊 Interactive Charts</h3>
                                <p style="margin: 0; color: #7f8c8d;">Dynamic pie charts with hover effects</p>
                            </div>
                            <div style="background: #ecf0f1; padding: 20px; border-radius: 8px; border-left: 4px solid #27ae60;">
                                <h3 style="margin: 0 0 10px 0; color: #27ae60;">📱 Responsive Design</h3>
                                <p style="margin: 0; color: #7f8c8d;">Works perfectly on all devices</p>
                            </div>
                            <div style="background: #ecf0f1; padding: 20px; border-radius: 8px; border-left: 4px solid #f39c12;">
                                <h3 style="margin: 0 0 10px 0; color: #f39c12;">🎨 Professional Styling</h3>
                                <p style="margin: 0; color: #7f8c8d;">Modern and clean interface</p>
                            </div>
                        </div>
                        
                        <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; color: white;">
                            <h3 style="margin: 0 0 10px 0;">🚀 Quick Actions</h3>
                            <p style="margin: 0 0 15px 0;">Access standard reports for detailed information:</p>
                            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                                <a href="overview-features.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; transition: all 0.3s ease;">📋 Features</a>
                                <a href="overview-steps.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; transition: all 0.3s ease;">👣 Steps</a>
                                <a href="overview-tags.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; transition: all 0.3s ease;">🏷️ Tags</a>
                                <a href="overview-failures.html" style="background: rgba(255,255,255,0.2); color: white; padding: 8px 16px; border-radius: 4px; text-decoration: none; transition: all 0.3s ease;">❌ Failures</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Other tabs would be here -->
            <section id="features" class="tab-content">
                <div class="chart-container">
                    <h2 class="chart-title">📋 Features Overview</h2>
                    <p style="text-align: center; color: #7f8c8d;">
                        <a href="overview-features.html" style="color: #3498db; text-decoration: none; font-size: 1.1em;">
                            → View Detailed Features Report
                        </a>
                    </p>
                </div>
            </section>

            <section id="scenarios" class="tab-content">
                <div class="chart-container">
                    <h2 class="chart-title">🎯 Scenarios Overview</h2>
                    <p style="text-align: center; color: #7f8c8d;">
                        Scenario details are available in the features report.
                    </p>
                </div>
            </section>

            <section id="steps" class="tab-content">
                <div class="chart-container">
                    <h2 class="chart-title">👣 Steps Overview</h2>
                    <p style="text-align: center; color: #7f8c8d;">
                        <a href="overview-steps.html" style="color: #3498db; text-decoration: none; font-size: 1.1em;">
                            → View Detailed Steps Report
                        </a>
                    </p>
                </div>
            </section>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="js/enhanced-charts.js"></script>
    <script>
        // Sample data for demonstration
        const testData = {
            passed: 2,
            failed: 1,
            skipped: 0,
            total: 3,
            duration: 6.3
        };

        // Initialize page with sample data
        document.addEventListener('DOMContentLoaded', function() {
            updateStatistics(testData);
            initializeTabNavigation();
        });

        function updateStatistics(data) {
            document.getElementById('passed-count').textContent = data.passed;
            document.getElementById('failed-count').textContent = data.failed;
            document.getElementById('skipped-count').textContent = data.skipped;
            document.getElementById('total-count').textContent = data.total;
            document.getElementById('pass-rate').textContent = Math.round((data.passed / data.total) * 100) + '%';
            document.getElementById('duration').textContent = data.duration + 's';
        }

        function initializeTabNavigation() {
            const tabs = document.querySelectorAll('.nav-tabs a');
            const contents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));
                    
                    // Add active class to clicked tab
                    this.classList.add('active');
                    
                    // Show corresponding content
                    const targetId = this.getAttribute('href').substring(1);
                    const targetContent = document.getElementById(targetId);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });
        }
    </script>
</body>
</html>
