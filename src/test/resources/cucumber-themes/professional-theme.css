/* 
 * Professional Theme for Cucumber Reports
 * Enhanced styling for better visual appeal and readability
 */

/* Global Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #ecf0f1;
    --dark-bg: #34495e;
    --text-color: #2c3e50;
    --border-color: #bdc3c7;
    --shadow: 0 2px 10px rgba(0,0,0,0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Body and Layout */
body {
    font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
    color: white;
    padding: 30px;
    text-align: center;
    position: relative;
}

.header::before {
    content: '🥒';
    font-size: 3em;
    position: absolute;
    top: 20px;
    left: 30px;
    opacity: 0.3;
}

.header h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 300;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header .subtitle {
    font-size: 1.2em;
    opacity: 0.9;
    margin-top: 10px;
}

/* Navigation */
.nav-tabs {
    background: var(--light-bg);
    border-bottom: 3px solid var(--secondary-color);
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
}

.nav-tabs li {
    list-style: none;
    margin: 0;
}

.nav-tabs a {
    display: block;
    padding: 15px 25px;
    text-decoration: none;
    color: var(--text-color);
    border-right: 1px solid var(--border-color);
    transition: var(--transition);
    font-weight: 500;
}

.nav-tabs a:hover,
.nav-tabs .active a {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
}

/* Statistics Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 30px;
    background: var(--light-bg);
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
    border-left: 5px solid var(--secondary-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 3em;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1em;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Success/Failure specific colors */
.stat-card.success .stat-number { color: var(--success-color); border-left-color: var(--success-color); }
.stat-card.failure .stat-number { color: var(--danger-color); border-left-color: var(--danger-color); }
.stat-card.pending .stat-number { color: var(--warning-color); border-left-color: var(--warning-color); }

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table th {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.table tr:hover td {
    background: var(--light-bg);
}

/* Status indicators */
.passed { color: var(--success-color); font-weight: bold; }
.failed { color: var(--danger-color); font-weight: bold; }
.skipped { color: var(--warning-color); font-weight: bold; }
.pending { color: var(--warning-color); font-weight: bold; }

/* Progress bars */
.progress {
    background: var(--light-bg);
    border-radius: 20px;
    height: 20px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color) 0%, var(--secondary-color) 100%);
    transition: width 0.6s ease;
    border-radius: 20px;
}

/* Charts and Graphs */
.chart-container {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 20px 0;
}

.chart-title {
    font-size: 1.5em;
    color: var(--primary-color);
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .table {
        font-size: 0.9em;
    }
}

/* Charts and Data Visualization */
.chart-container {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 20px 0;
    position: relative;
}

.chart-title {
    font-size: 1.5em;
    color: var(--primary-color);
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
}

.chart-wrapper {
    position: relative;
    height: 300px;
    margin: 20px 0;
}

/* Progress Bars and Metrics */
.progress-container {
    margin: 15px 0;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: var(--light-bg);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color) 0%, var(--secondary-color) 100%);
    border-radius: 10px;
    transition: width 1s ease-in-out;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Donut Chart CSS */
.donut-chart {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 20px auto;
}

.donut-chart svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.donut-chart .donut-segment {
    fill: none;
    stroke-width: 20;
    transition: stroke-width 0.3s ease;
}

.donut-chart .donut-segment:hover {
    stroke-width: 25;
}

.donut-chart .donut-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.donut-chart .donut-percentage {
    font-size: 2em;
    font-weight: bold;
    color: var(--primary-color);
}

.donut-chart .donut-label {
    font-size: 0.9em;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Bar Chart CSS */
.bar-chart {
    display: flex;
    align-items: flex-end;
    height: 200px;
    padding: 20px;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    margin: 20px 0;
}

.bar-chart .bar {
    flex: 1;
    margin: 0 5px;
    background: linear-gradient(to top, var(--secondary-color), var(--primary-color));
    border-radius: 4px 4px 0 0;
    position: relative;
    transition: all 0.3s ease;
    min-height: 10px;
}

.bar-chart .bar:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.bar-chart .bar::after {
    content: attr(data-value);
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8em;
    font-weight: bold;
    color: var(--text-color);
}

.bar-chart .bar-label {
    position: absolute;
    bottom: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8em;
    color: #666;
    text-align: center;
}

/* Line Chart CSS */
.line-chart {
    position: relative;
    height: 250px;
    background: white;
    border-radius: var(--border-radius);
    padding: 20px;
    margin: 20px 0;
}

.line-chart svg {
    width: 100%;
    height: 100%;
}

.line-chart .line-path {
    fill: none;
    stroke: var(--secondary-color);
    stroke-width: 3;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.line-chart .line-area {
    fill: url(#lineGradient);
    opacity: 0.3;
}

.line-chart .data-point {
    fill: var(--secondary-color);
    stroke: white;
    stroke-width: 2;
    r: 4;
    transition: r 0.3s ease;
}

.line-chart .data-point:hover {
    r: 6;
    fill: var(--primary-color);
}

/* Gauge Chart CSS */
.gauge-chart {
    position: relative;
    width: 200px;
    height: 120px;
    margin: 20px auto;
}

.gauge-chart svg {
    width: 100%;
    height: 100%;
}

.gauge-background {
    fill: none;
    stroke: var(--light-bg);
    stroke-width: 15;
}

.gauge-fill {
    fill: none;
    stroke: var(--success-color);
    stroke-width: 15;
    stroke-linecap: round;
    transition: stroke-dasharray 1s ease-in-out;
}

.gauge-text {
    text-anchor: middle;
    font-size: 1.5em;
    font-weight: bold;
    fill: var(--primary-color);
}

/* Heatmap CSS */
.heatmap {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    padding: 20px;
    background: white;
    border-radius: var(--border-radius);
    margin: 20px 0;
}

.heatmap-cell {
    aspect-ratio: 1;
    border-radius: 2px;
    transition: all 0.3s ease;
    position: relative;
}

.heatmap-cell:hover {
    transform: scale(1.2);
    z-index: 10;
}

.heatmap-cell.level-0 { background: #ebedf0; }
.heatmap-cell.level-1 { background: #c6e48b; }
.heatmap-cell.level-2 { background: #7bc96f; }
.heatmap-cell.level-3 { background: #239a3b; }
.heatmap-cell.level-4 { background: #196127; }

/* Tooltip for charts */
.chart-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.9em;
    pointer-events: none;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.chart-tooltip.visible {
    opacity: 1;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-color: #ecf0f1;
        --light-bg: #2c3e50;
        --border-color: #34495e;
    }

    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .container {
        background: #34495e;
        color: var(--text-color);
    }

    .table td {
        background: #34495e;
        color: var(--text-color);
    }

    .stat-card {
        background: #2c3e50;
        color: var(--text-color);
    }

    .chart-container,
    .line-chart {
        background: #2c3e50;
        color: var(--text-color);
    }

    .bar-chart {
        background: #34495e;
    }

    .heatmap {
        background: #2c3e50;
    }
}

/* Animation effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Print styles */
@media print {
    body {
        background: white;
        color: black;
    }
    
    .header {
        background: white;
        color: black;
        border-bottom: 2px solid black;
    }
    
    .nav-tabs,
    .chart-container {
        page-break-inside: avoid;
    }
}
