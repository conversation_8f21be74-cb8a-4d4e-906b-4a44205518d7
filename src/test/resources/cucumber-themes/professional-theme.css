/* Enhanced Cucumber Report Theme */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #ecf0f1;
    --dark-bg: #34495e;
    --text-color: #2c3e50;
    --border-color: #bdc3c7;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Enhanced Body and Layout */
body {
    font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

/* Enhanced Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    pointer-events: none;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 2;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-icon {
    font-size: 3em;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.header-text h1 {
    margin: 0;
    font-size: 2.2em;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-text .subtitle {
    font-size: 1em;
    opacity: 0.9;
    margin-top: 5px;
    font-weight: 300;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-stats {
    display: flex;
    gap: 20px;
}

.header-stat {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255,255,255,0.1);
    padding: 12px 16px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
    min-width: 80px;
}

.header-stat:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.header-stat .stat-icon {
    font-size: 1.5em;
}

.header-stat .stat-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.header-stat .stat-value {
    font-size: 1.4em;
    font-weight: bold;
    line-height: 1;
}

.header-stat .stat-label {
    font-size: 0.8em;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 2px;
}

.header-stat.success .stat-value { color: #2ecc71; }
.header-stat.danger .stat-value { color: #e74c3c; }
.header-stat.info .stat-value { color: #3498db; }
.header-stat.primary .stat-value { color: #f39c12; }

/* Enhanced Statistics Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 30px;
    background: var(--light-bg);
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
    border-left: 5px solid var(--secondary-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 3em;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1em;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Status-specific colors */
.stat-card.success .stat-number { 
    color: var(--success-color); 
    border-left-color: var(--success-color); 
}

.stat-card.failure .stat-number { 
    color: var(--danger-color); 
    border-left-color: var(--danger-color); 
}

.stat-card.pending .stat-number { 
    color: var(--warning-color); 
    border-left-color: var(--warning-color); 
}

/* Enhanced Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table th {
    background: var(--primary-color);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
}

.table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.table tr:hover td {
    background: var(--light-bg);
}

/* Enhanced Navigation */
.nav-tabs {
    background: var(--light-bg);
    border-bottom: 3px solid var(--secondary-color);
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
}

.nav-tabs li {
    list-style: none;
    margin: 0;
}

.nav-tabs a {
    display: block;
    padding: 15px 25px;
    text-decoration: none;
    color: var(--text-color);
    border-right: 1px solid var(--border-color);
    transition: var(--transition);
    font-weight: 500;
}

.nav-tabs a:hover,
.nav-tabs .active a {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
}

/* Enhanced Charts Container */
.chart-container {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 20px 0;
}

.chart-title {
    font-size: 1.5em;
    color: var(--primary-color);
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
}

/* Enhanced Pie Chart */
.pie-chart-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
    margin: 20px 0;
}

.pie-chart {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: conic-gradient(
        var(--success-color) 0deg 120deg,
        var(--danger-color) 120deg 150deg,
        var(--warning-color) 150deg 180deg,
        var(--secondary-color) 180deg 360deg
    );
    position: relative;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.pie-chart:hover {
    transform: scale(1.05);
}

/* Enhanced Legend */
.pie-legend {
    flex: 1;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 15px 0;
    padding: 10px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.legend-item:hover {
    background: var(--light-bg);
    transform: translateX(5px);
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.legend-color.passed { background: var(--success-color); }
.legend-color.failed { background: var(--danger-color); }
.legend-color.skipped { background: var(--warning-color); }
.legend-color.pending { background: var(--secondary-color); }

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .table {
        font-size: 0.9em;
    }
    
    .pie-chart-container {
        flex-direction: column;
        text-align: center;
    }
    
    .pie-chart {
        width: 250px;
        height: 250px;
    }
}

/* Animation effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Enhanced styling for better visual appeal */
.content {
    padding: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.5s ease-out;
}
