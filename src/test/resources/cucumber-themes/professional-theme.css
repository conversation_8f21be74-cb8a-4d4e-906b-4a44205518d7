/* Modern Cucumber Report Theme */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --dark-bg: #343a40;
    --sidebar-bg: #2c3e50;
    --sidebar-hover: #34495e;
    --text-color: #495057;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --transition: all 0.15s ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    background-color: var(--light-bg);
    color: var(--text-color);
    line-height: 1.5;
    font-size: 0.875rem;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Top Header */
.top-header {
    background: var(--dark-bg);
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow);
    position: relative;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.header-title h1 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: white;
}

.header-subtitle {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 400;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.header-metrics {
    display: flex;
    gap: 1.5rem;
}

.metric {
    text-align: center;
}

.metric-value {
    display: block;
    font-size: 1.125rem;
    font-weight: 700;
    color: blue;
    line-height: 1;
}

.metric-label {
    display: block;
    font-size: 0.625rem;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

.header-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-action {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.btn-action:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.btn-action.primary {
    background: var(--success-color);
    border-color: var(--success-color);
}

.btn-action.primary:hover {
    background: #219a52;
    border-color: #219a52;
}

/* Main Layout */
.main-layout {
    display: flex;
    flex: 1;
    min-height: calc(100vh - 4rem);
}

/* Sidebar */
.sidebar {
    width: 240px;
    background: var(--sidebar-bg);
    color: white;
    display: flex;
    flex-direction: column;
    box-shadow: var(--shadow);
    position: relative;
    z-index: 100;
}

.sidebar-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h3 {
    font-size: 0.875rem;
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: rgba(255, 255, 255, 0.8);
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: none;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar-nav {
    flex: 1;
    padding: 1rem 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
    gap: 0.75rem;
}

.nav-item:hover {
    background: var(--sidebar-hover);
    color: white;
    border-left-color: rgba(255, 255, 255, 0.3);
}

.nav-item.active {
    background: var(--sidebar-hover);
    color: white;
    border-left-color: var(--success-color);
}

.nav-icon {
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
}

.nav-text {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Main Content */
.main-content {
    flex: 1;
    background: var(--light-bg);
    overflow-y: auto;
    padding: 1.5rem;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Stats Overview */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--secondary-color);
}

.stat-card.passed::before { background: var(--success-color); }
.stat-card.failed::before { background: var(--danger-color); }
.stat-card.skipped::before { background: var(--warning-color); }
.stat-card.features::before { background: var(--info-color); }
.stat-card.scenarios::before { background: var(--primary-color); }

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card .stat-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.stat-content {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-color);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.stat-card.passed .stat-number { color: var(--success-color); }
.stat-card.failed .stat-number { color: var(--danger-color); }
.stat-card.skipped .stat-number { color: var(--warning-color); }
.stat-card.features .stat-number { color: var(--info-color); }
.stat-card.scenarios .stat-number { color: var(--primary-color); }

.stat-label {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-muted);
    margin-bottom: 0.25rem;
}

.stat-trend {
    font-size: 0.625rem;
    color: var(--text-muted);
}

/* Overview Section */
.overview-section {
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid var(--border-color);
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.section-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.75rem;
    color: var(--text-muted);
}

.meta-item {
    background: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

/* Overview Grid */
.overview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.overview-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.card-header {
    background: var(--light-bg);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.card-content {
    padding: 1.5rem;
}

/* Pie Chart */
.pie-chart-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.pie-chart {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: conic-gradient(
        var(--success-color) 0deg 120deg,
        var(--danger-color) 120deg 150deg,
        var(--warning-color) 150deg 180deg,
        var(--secondary-color) 180deg 360deg
    );
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
}

.pie-chart:hover {
    transform: scale(1.02);
}

.pie-chart-center {
    background: white;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: inset var(--shadow);
}

.pie-chart-percentage {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    line-height: 1;
}

.pie-chart-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

/* Legend */
.pie-legend {
    flex: 1;
}

.legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.legend-item:hover {
    background: #e9ecef;
    transform: translateX(4px);
}

.legend-text {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: var(--shadow);
}

.legend-color.passed { background: var(--success-color); }
.legend-color.failed { background: var(--danger-color); }
.legend-color.skipped { background: var(--warning-color); }

.legend-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-color);
}

.legend-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-color);
}

/* Performance Metrics */
.performance-metrics {
    display: flex;
    gap: 2rem;
    justify-content: center;
}

.metric-gauge {
    text-align: center;
}

.gauge-container {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto 1rem;
}

.gauge {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(
        var(--success-color) 0deg 270deg,
        var(--border-color) 270deg 360deg
    );
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gauge-center {
    background: white;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: inset var(--shadow);
}

.gauge-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-color);
    line-height: 1;
}

.gauge-label {
    font-size: 0.625rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

/* Features Summary */
.features-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.summary-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.summary-content {
    flex: 1;
}

.summary-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-color);
    line-height: 1;
    margin-bottom: 0.25rem;
}

.summary-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

/* Enhanced Tables */
.features-table-container,
.scenarios-table-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.table-header {
    background: var(--light-bg);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.table-filters {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.filter-select,
.search-input {
    padding: 0.375rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    background: white;
    color: var(--text-color);
}

.filter-select:focus,
.search-input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.search-input {
    min-width: 200px;
}

.enhanced-table {
    overflow-x: auto;
}

.features-table,
.scenarios-table {
    width: 100%;
    border-collapse: collapse;
}

.features-table th,
.scenarios-table th {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.features-table td,
.scenarios-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    vertical-align: middle;
}

.features-table tr:hover,
.scenarios-table tr:hover {
    background: var(--light-bg);
}

.loading-row {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 2rem !important;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.passed {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.status-badge.failed {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.status-badge.skipped {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

/* Scenario Drill-down */
.scenario-row {
    cursor: pointer;
    transition: var(--transition);
}

.scenario-row:hover {
    background: rgba(52, 152, 219, 0.05) !important;
}

.scenario-name {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.expand-icon {
    font-size: 0.75rem;
    color: var(--secondary-color);
    transition: var(--transition);
    width: 12px;
    text-align: center;
}

.scenario-details {
    background: var(--light-bg) !important;
}

.scenario-details:hover {
    background: var(--light-bg) !important;
}

.steps-container {
    padding: 1rem;
    border-left: 3px solid var(--secondary-color);
    margin: 0.5rem 0;
}

.steps-container h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.steps-container h4::before {
    content: '👣';
    font-size: 1.2rem;
}

.steps-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.step-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: white;
    border-radius: var(--border-radius);
    border-left: 3px solid var(--border-color);
    transition: var(--transition);
}

.step-item:hover {
    box-shadow: var(--shadow);
    transform: translateX(2px);
}

.step-item.passed {
    border-left-color: var(--success-color);
}

.step-item.failed {
    border-left-color: var(--danger-color);
    background: rgba(231, 76, 60, 0.02);
}

.step-item.skipped {
    border-left-color: var(--warning-color);
    background: rgba(243, 156, 18, 0.02);
}

.step-keyword {
    font-weight: 700;
    color: var(--primary-color);
    min-width: 60px;
    font-size: 0.875rem;
    text-transform: uppercase;
}

.step-name {
    flex: 1;
    color: var(--text-color);
    font-size: 0.875rem;
    line-height: 1.4;
}

.step-duration {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-weight: 500;
    min-width: 50px;
    text-align: right;
}

.step-status {
    font-size: 0.625rem;
    padding: 0.25rem 0.5rem;
}

/* Steps Summary */
.steps-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

/* Step Types */
.step-types {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.step-type-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.step-type-label {
    font-weight: 600;
    min-width: 60px;
    font-size: 0.875rem;
    color: var(--text-color);
}

.step-type-bar {
    flex: 1;
    height: 20px;
    background: var(--border-color);
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.step-type-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
}

.step-type-fill.given { background: var(--info-color); }
.step-type-fill.when { background: var(--warning-color); }
.step-type-fill.then { background: var(--success-color); }
.step-type-fill.and { background: var(--secondary-color); }

.step-type-count {
    font-weight: 600;
    min-width: 30px;
    text-align: right;
    font-size: 0.875rem;
    color: var(--text-color);
}

/* Slowest Steps */
.slowest-steps {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.slow-step-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--warning-color);
}

.slow-step-name {
    font-size: 0.875rem;
    color: var(--text-color);
    flex: 1;
    margin-right: 1rem;
}

.slow-step-duration {
    font-weight: 600;
    color: var(--warning-color);
    font-size: 0.875rem;
}

/* Analytics */
.analytics-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}


.metric-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
}

.metric-description {
    font-size: 0.875rem;
    color: var(--text-muted);
    line-height: 1.4;
}

/* Time Analysis */
.time-analysis {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.time-metric {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-bg);
    border-radius: var(--border-radius);
}

.time-label {
    font-size: 0.875rem;
    color: var(--text-color);
    font-weight: 500;
}

.time-value {
    font-weight: 600;
    color: var(--secondary-color);
    font-size: 0.875rem;
}

/* Insights */
.insights-section {
    margin-top: 2rem;
}

.insights-header {
    margin-bottom: 1.5rem;
}

.insights-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.insight-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
}

.insight-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

.insight-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0 0 0.5rem 0;
}

.insight-content p {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin: 0;
    line-height: 1.4;
}

/* Top Features */
.top-features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.top-feature-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--success-color);
}

.top-feature-name {
    font-size: 0.875rem;
    color: var(--text-color);
    font-weight: 500;
    flex: 1;
    margin-right: 1rem;
}

.top-feature-score {
    font-weight: 600;
    color: var(--success-color);
    font-size: 0.875rem;
}

.loading-text {
    text-align: center;
    color: var(--text-muted);
    font-style: italic;
    padding: 2rem;
}

/* Trends */
.trends-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.trend-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.trend-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.trend-card.improving::before { background: var(--success-color); }
.trend-card.stable::before { background: var(--info-color); }
.trend-card.warning::before { background: var(--warning-color); }
.trend-card.info::before { background: var(--secondary-color); }

.trend-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.trend-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.trend-content {
    flex: 1;
}

.trend-value {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.trend-card.improving .trend-value { color: var(--success-color); }
.trend-card.stable .trend-value { color: var(--info-color); }
.trend-card.warning .trend-value { color: var(--warning-color); }
.trend-card.info .trend-value { color: var(--secondary-color); }

.trend-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.trend-description {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Chart Controls */
.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-period {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    background: white;
    color: var(--text-color);
}

.trend-chart {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    color: var(--text-muted);
    font-style: italic;
}

/* Health Metrics */
.health-metrics {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    overflow: hidden;
    margin-bottom: 2rem;
}

.metrics-header {
    background: var(--light-bg);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.metrics-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.health-score {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.score-label {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.score-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--success-color);
}

.health-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    padding: 1.5rem;
}

.health-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.health-item:hover {
    background: #e9ecef;
    transform: translateX(2px);
}

.health-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.health-indicator.excellent { background: var(--success-color); }
.health-indicator.good { background: var(--info-color); }
.health-indicator.warning { background: var(--warning-color); }
.health-indicator.danger { background: var(--danger-color); }

.health-info {
    flex: 1;
}

.health-metric {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.health-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.health-trend {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Trend Insights */
.trend-insights {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    overflow: hidden;
    margin-bottom: 2rem;
}

.insights-header {
    background: var(--light-bg);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.insights-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.insights-timeline {
    padding: 1.5rem;
}

.timeline-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 60px;
    top: 2rem;
    bottom: -1.5rem;
    width: 2px;
    background: var(--border-color);
}

.timeline-date {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-muted);
    min-width: 80px;
    text-align: right;
    padding-top: 0.25rem;
}

.timeline-content {
    flex: 1;
    padding: 0.75rem 1rem;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--border-color);
    transition: var(--transition);
}

.timeline-item.positive .timeline-content {
    border-left-color: var(--success-color);
    background: rgba(39, 174, 96, 0.05);
}

.timeline-item.warning .timeline-content {
    border-left-color: var(--warning-color);
    background: rgba(243, 156, 18, 0.05);
}

.timeline-item.neutral .timeline-content {
    border-left-color: var(--info-color);
    background: rgba(23, 162, 184, 0.05);
}

.timeline-content:hover {
    transform: translateX(2px);
    box-shadow: var(--shadow);
}

.timeline-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.timeline-description {
    font-size: 0.875rem;
    color: var(--text-muted);
    line-height: 1.4;
}

/* Predictive Analysis */
.predictive-analysis {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.prediction-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.prediction-header h3 {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
}

.confidence-score {
    font-size: 0.875rem;
    opacity: 0.9;
}

.prediction-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    padding: 1.5rem;
}

.prediction-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.prediction-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.prediction-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

.prediction-content {
    flex: 1;
}

.prediction-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.prediction-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--secondary-color);
    margin-bottom: 0.25rem;
}

.prediction-description {
    font-size: 0.75rem;
    color: var(--text-muted);
    line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .overview-grid {
        grid-template-columns: 1fr;
    }

    .pie-chart-container {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .performance-metrics {
        flex-direction: column;
        gap: 1.5rem;
    }

    .features-summary,
    .steps-summary,
    .trends-summary {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .analytics-metrics {
        grid-template-columns: 1fr;
    }

    .health-grid,
    .prediction-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .main-layout {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        order: 2;
    }

    .sidebar-toggle {
        display: block;
    }

    .main-content {
        order: 1;
        padding: 1rem;
    }

    .top-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-metrics {
        justify-content: center;
    }

    .stats-overview {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.75rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .section-header {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }

    .pie-chart {
        width: 150px;
        height: 150px;
    }

    .pie-chart-center {
        width: 90px;
        height: 90px;
    }

    .gauge-container {
        width: 100px;
        height: 100px;
    }

    .gauge-center {
        width: 70px;
        height: 70px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 0.75rem;
    }

    .stats-overview {
        grid-template-columns: 1fr 1fr;
    }

    .stat-card {
        padding: 0.75rem;
    }

    .stat-number {
        font-size: 1.25rem;
    }

    .stat-label {
        font-size: 0.625rem;
    }

    .redirect-content {
        padding: 2rem 1rem;
    }

    .btn-redirect {
        padding: 0.625rem 1.25rem;
        font-size: 0.75rem;
    }
}

/* Animation effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Enhanced styling for better visual appeal */
.content {
    padding: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.5s ease-out;
}
