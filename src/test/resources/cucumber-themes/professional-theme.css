/* Enhanced Cucumber Report Theme */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-bg: #ecf0f1;
    --dark-bg: #34495e;
    --text-color: #2c3e50;
    --border-color: #bdc3c7;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

/* Enhanced Body and Layout */
body {
    font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    padding: 20px;
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
}

/* Enhanced Header */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--dark-bg) 100%);
    color: white;
    padding: 30px;
    text-align: center;
    position: relative;
}

.header::before {
    content: '🥒';
    font-size: 3em;
    position: absolute;
    top: 20px;
    left: 30px;
    opacity: 0.3;
}

.header h1 {
    margin: 0;
    font-size: 2.5em;
    font-weight: 300;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header .subtitle {
    font-size: 1.2em;
    opacity: 0.9;
    margin-top: 10px;
}

/* Enhanced Statistics Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 30px;
    background: var(--light-bg);
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
    border-left: 5px solid var(--secondary-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-number {
    font-size: 3em;
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.stat-label {
    font-size: 1.1em;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Status-specific colors */
.stat-card.success .stat-number { 
    color: var(--success-color); 
    border-left-color: var(--success-color); 
}

.stat-card.failure .stat-number { 
    color: var(--danger-color); 
    border-left-color: var(--danger-color); 
}

.stat-card.pending .stat-number { 
    color: var(--warning-color); 
    border-left-color: var(--warning-color); 
}

/* Enhanced Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table th {
    background: var(--primary-color);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
}

.table td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.table tr:hover td {
    background: var(--light-bg);
}

/* Enhanced Navigation */
.nav-tabs {
    background: var(--light-bg);
    border-bottom: 3px solid var(--secondary-color);
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
}

.nav-tabs li {
    list-style: none;
    margin: 0;
}

.nav-tabs a {
    display: block;
    padding: 15px 25px;
    text-decoration: none;
    color: var(--text-color);
    border-right: 1px solid var(--border-color);
    transition: var(--transition);
    font-weight: 500;
}

.nav-tabs a:hover,
.nav-tabs .active a {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
}

/* Enhanced Charts Container */
.chart-container {
    background: white;
    padding: 30px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 20px 0;
}

.chart-title {
    font-size: 1.5em;
    color: var(--primary-color);
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
}

/* Enhanced Pie Chart */
.pie-chart-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
    margin: 20px 0;
}

.pie-chart {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: conic-gradient(
        var(--success-color) 0deg 120deg,
        var(--danger-color) 120deg 150deg,
        var(--warning-color) 150deg 180deg,
        var(--secondary-color) 180deg 360deg
    );
    position: relative;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.pie-chart:hover {
    transform: scale(1.05);
}

/* Enhanced Legend */
.pie-legend {
    flex: 1;
}

.legend-item {
    display: flex;
    align-items: center;
    margin: 15px 0;
    padding: 10px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.legend-item:hover {
    background: var(--light-bg);
    transform: translateX(5px);
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-right: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.legend-color.passed { background: var(--success-color); }
.legend-color.failed { background: var(--danger-color); }
.legend-color.skipped { background: var(--warning-color); }
.legend-color.pending { background: var(--secondary-color); }

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2em;
    }
    
    .nav-tabs {
        flex-direction: column;
    }
    
    .stats-container {
        grid-template-columns: 1fr;
        padding: 20px;
    }
    
    .stat-card {
        padding: 20px;
    }
    
    .table {
        font-size: 0.9em;
    }
    
    .pie-chart-container {
        flex-direction: column;
        text-align: center;
    }
    
    .pie-chart {
        width: 250px;
        height: 250px;
    }
}

/* Animation effects */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Enhanced styling for better visual appeal */
.content {
    padding: 20px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeInUp 0.5s ease-out;
}
