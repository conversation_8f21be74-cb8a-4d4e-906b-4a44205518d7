<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🥒 Enhanced Cucumber Test Report</title>
    <link rel="stylesheet" href="professional-theme.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🥒</text></svg>">
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <h1>Cucumber Test Report</h1>
            <div class="subtitle">Enhanced Visual Analytics Dashboard</div>
        </header>

        <!-- Navigation Tabs -->
        <nav>
            <ul class="nav-tabs">
                <li><a href="#overview" class="active">📊 Overview</a></li>
                <li><a href="#features">📋 Features</a></li>
                <li><a href="#scenarios">🎯 Scenarios</a></li>
                <li><a href="#steps">👣 Steps</a></li>
                <li><a href="#analytics">📈 Analytics</a></li>
                <li><a href="#trends">📉 Trends</a></li>
            </ul>
        </nav>

        <!-- Statistics Cards -->
        <section class="stats-container">
            <div class="stat-card success">
                <div class="stat-number" id="passed-count">0</div>
                <div class="stat-label">Passed</div>
            </div>
            <div class="stat-card failure">
                <div class="stat-number" id="failed-count">0</div>
                <div class="stat-label">Failed</div>
            </div>
            <div class="stat-card pending">
                <div class="stat-number" id="skipped-count">0</div>
                <div class="stat-label">Skipped</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="total-count">0</div>
                <div class="stat-label">Total</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pass-rate">0%</div>
                <div class="stat-label">Pass Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="duration">0s</div>
                <div class="stat-label">Duration</div>
            </div>
        </section>

        <!-- Main Content Area -->
        <main class="content">
            <!-- Overview Tab -->
            <section id="overview" class="tab-content active">
                <!-- Pie Chart Section -->
                <div class="chart-container">
                    <h2 class="chart-title">📊 Test Results Distribution</h2>
                    <div class="pie-chart-container">
                        <div class="pie-chart" data-chart-data='{"passed":85,"failed":10,"skipped":5}'>
                            <div class="pie-chart-center">
                                <div class="pie-chart-percentage">0%</div>
                                <div class="pie-chart-label">Success Rate</div>
                            </div>
                        </div>
                        <div class="pie-legend">
                            <div class="legend-item">
                                <div class="legend-color passed"></div>
                                <div class="legend-text">
                                    <span class="legend-label">Passed</span>
                                    <span class="legend-value">85</span>
                                </div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color failed"></div>
                                <div class="legend-text">
                                    <span class="legend-label">Failed</span>
                                    <span class="legend-value">10</span>
                                </div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color skipped"></div>
                                <div class="legend-text">
                                    <span class="legend-label">Skipped</span>
                                    <span class="legend-value">5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bar Chart Section -->
                <div class="chart-container">
                    <h2 class="chart-title">📈 Feature Performance</h2>
                    <div class="bar-chart">
                        <div class="bar-chart-title">Test Results by Feature</div>
                        <div class="bar-item">
                            <div class="bar-label">Authentication</div>
                            <div class="bar-visual">
                                <div class="bar-fill" data-width="95%" style="width: 0%;"></div>
                            </div>
                            <div class="bar-value">95%</div>
                        </div>
                        <div class="bar-item">
                            <div class="bar-label">Session Management</div>
                            <div class="bar-visual">
                                <div class="bar-fill" data-width="88%" style="width: 0%;"></div>
                            </div>
                            <div class="bar-value">88%</div>
                        </div>
                        <div class="bar-item">
                            <div class="bar-label">API Validation</div>
                            <div class="bar-visual">
                                <div class="bar-fill" data-width="92%" style="width: 0%;"></div>
                            </div>
                            <div class="bar-value">92%</div>
                        </div>
                        <div class="bar-item">
                            <div class="bar-label">Device Management</div>
                            <div class="bar-visual">
                                <div class="bar-fill" data-width="78%" style="width: 0%;"></div>
                            </div>
                            <div class="bar-value">78%</div>
                        </div>
                    </div>
                </div>

                <!-- Gauge Charts Section -->
                <div class="chart-container">
                    <h2 class="chart-title">🎯 Performance Metrics</h2>
                    <div class="gauge-container">
                        <div class="gauge" data-value="85">
                            <div class="gauge-background">
                                <div class="gauge-needle"></div>
                            </div>
                            <div class="gauge-value">0%</div>
                            <div class="gauge-label">Overall Success</div>
                        </div>
                        <div class="gauge" data-value="92">
                            <div class="gauge-background">
                                <div class="gauge-needle"></div>
                            </div>
                            <div class="gauge-value">0%</div>
                            <div class="gauge-label">Code Coverage</div>
                        </div>
                        <div class="gauge" data-value="78">
                            <div class="gauge-background">
                                <div class="gauge-needle"></div>
                            </div>
                            <div class="gauge-value">0%</div>
                            <div class="gauge-label">Performance</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Tab -->
            <section id="analytics" class="tab-content">
                <!-- Line Chart Section -->
                <div class="chart-container">
                    <h2 class="chart-title">📈 Test Execution Timeline</h2>
                    <div class="line-chart" data-chart-data='[65,70,75,80,85,88,92,89,91,95]'></div>
                </div>

                <!-- Data Cards Section -->
                <div class="data-cards">
                    <div class="data-card">
                        <div class="data-card-header">
                            <h3 class="data-card-title">Execution Summary</h3>
                            <div class="data-card-icon">⚡</div>
                        </div>
                        <div class="data-card-content">
                            <div class="data-metric">
                                <span class="metric-label">Total Tests</span>
                                <span class="metric-value">100</span>
                            </div>
                            <div class="data-metric">
                                <span class="metric-label">Avg Duration</span>
                                <span class="metric-value">2.5s</span>
                            </div>
                            <div class="data-metric">
                                <span class="metric-label">Success Rate</span>
                                <span class="metric-value">85%</span>
                            </div>
                        </div>
                    </div>

                    <div class="data-card">
                        <div class="data-card-header">
                            <h3 class="data-card-title">Quality Metrics</h3>
                            <div class="data-card-icon">🎯</div>
                        </div>
                        <div class="data-card-content">
                            <div class="data-metric">
                                <span class="metric-label">Code Coverage</span>
                                <span class="metric-value">92%</span>
                            </div>
                            <div class="data-metric">
                                <span class="metric-label">Test Stability</span>
                                <span class="metric-value">
                                    <span class="trend-indicator up" data-trend="up">
                                        <span class="trend-arrow"></span>
                                        +5%
                                    </span>
                                </span>
                            </div>
                            <div class="data-metric">
                                <span class="metric-label">Performance</span>
                                <span class="metric-value">
                                    <span class="trend-indicator stable" data-trend="stable">
                                        <span class="trend-arrow"></span>
                                        Stable
                                    </span>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="data-card">
                        <div class="data-card-header">
                            <h3 class="data-card-title">Environment Info</h3>
                            <div class="data-card-icon">🖥️</div>
                        </div>
                        <div class="data-card-content">
                            <div class="data-metric">
                                <span class="metric-label">Java Version</span>
                                <span class="metric-value">17.0.2</span>
                            </div>
                            <div class="data-metric">
                                <span class="metric-label">Spring Boot</span>
                                <span class="metric-value">3.1.0</span>
                            </div>
                            <div class="data-metric">
                                <span class="metric-label">Test Framework</span>
                                <span class="metric-value">Cucumber</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Comparison Chart -->
                <div class="chart-container">
                    <h2 class="chart-title">📊 Current vs Previous Run</h2>
                    <div class="comparison-chart">
                        <div class="comparison-item">
                            <div class="comparison-label">Passed Tests</div>
                            <div class="comparison-bars">
                                <div class="comparison-bar current" style="width: 85px;">85</div>
                                <div class="comparison-bar previous" style="width: 78px;">78</div>
                            </div>
                            <div class="comparison-values">
                                <div class="comparison-current">85</div>
                                <div class="comparison-previous">78</div>
                            </div>
                        </div>
                        <div class="comparison-item">
                            <div class="comparison-label">Failed Tests</div>
                            <div class="comparison-bars">
                                <div class="comparison-bar current" style="width: 10px;">10</div>
                                <div class="comparison-bar previous" style="width: 15px;">15</div>
                            </div>
                            <div class="comparison-values">
                                <div class="comparison-current">10</div>
                                <div class="comparison-previous">15</div>
                            </div>
                        </div>
                        <div class="comparison-item">
                            <div class="comparison-label">Duration</div>
                            <div class="comparison-bars">
                                <div class="comparison-bar current" style="width: 45px;">2.5s</div>
                                <div class="comparison-bar previous" style="width: 52px;">2.8s</div>
                            </div>
                            <div class="comparison-values">
                                <div class="comparison-current">2.5s</div>
                                <div class="comparison-previous">2.8s</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Trends Tab -->
            <section id="trends" class="tab-content">
                <!-- Heatmap Section -->
                <div class="chart-container">
                    <h2 class="chart-title">🔥 Test Activity Heatmap</h2>
                    <div class="heatmap">
                        <div class="heatmap-grid">
                            <!-- Heatmap cells will be generated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Timeline Section -->
                <div class="chart-container">
                    <h2 class="chart-title">⏱️ Execution Timeline</h2>
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker success">1</div>
                            <div class="timeline-content">
                                <div class="timeline-title">Authentication Tests</div>
                                <div class="timeline-description">All authentication scenarios passed successfully</div>
                                <div class="timeline-time">Completed in 45s</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker success">2</div>
                            <div class="timeline-content">
                                <div class="timeline-title">Session Management</div>
                                <div class="timeline-description">Session validation and management tests</div>
                                <div class="timeline-time">Completed in 32s</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker failed">3</div>
                            <div class="timeline-content">
                                <div class="timeline-title">API Validation</div>
                                <div class="timeline-description">Some API validation tests failed</div>
                                <div class="timeline-time">Completed in 28s</div>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker pending">4</div>
                            <div class="timeline-content">
                                <div class="timeline-title">Device Management</div>
                                <div class="timeline-description">Device management tests in progress</div>
                                <div class="timeline-time">Running...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- JavaScript -->
    <script src="enhanced-charts.js"></script>
    <script>
        // Sample data for demonstration
        const testData = {
            passed: 85,
            failed: 10,
            skipped: 5,
            total: 100,
            duration: 125.5
        };

        // Initialize page with sample data
        document.addEventListener('DOMContentLoaded', function() {
            updateStatistics(testData);
            initializeTabNavigation();
            generateHeatmapData();
        });

        function updateStatistics(data) {
            document.getElementById('passed-count').textContent = data.passed;
            document.getElementById('failed-count').textContent = data.failed;
            document.getElementById('skipped-count').textContent = data.skipped;
            document.getElementById('total-count').textContent = data.total;
            document.getElementById('pass-rate').textContent = Math.round((data.passed / data.total) * 100) + '%';
            document.getElementById('duration').textContent = data.duration + 's';
        }

        function initializeTabNavigation() {
            const tabs = document.querySelectorAll('.nav-tabs a');
            const contents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));
                    
                    // Add active class to clicked tab
                    this.classList.add('active');
                    
                    // Show corresponding content
                    const targetId = this.getAttribute('href').substring(1);
                    const targetContent = document.getElementById(targetId);
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });
        }

        function generateHeatmapData() {
            const heatmapGrid = document.querySelector('.heatmap-grid');
            const days = 49; // 7 weeks
            
            for (let i = 0; i < days; i++) {
                const cell = document.createElement('div');
                const level = Math.floor(Math.random() * 5);
                const value = Math.floor(Math.random() * 20);
                const date = new Date();
                date.setDate(date.getDate() - (days - i));
                
                cell.className = `heatmap-cell level-${level}`;
                cell.dataset.value = value;
                cell.dataset.date = date.toDateString();
                cell.textContent = value > 0 ? value : '';
                
                heatmapGrid.appendChild(cell);
            }
        }
    </script>
</body>
</html>
