package com.tyme.tymex.stepupauth.utils;

import net.masterthought.cucumber.Configuration;
import net.masterthought.cucumber.ReportBuilder;
import net.masterthought.cucumber.sorting.SortingMethod;
import lombok.extern.log4j.Log4j2;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Enhanced Utility class to generate beautiful HTML reports from Cucumber JSON results
 * using net.masterthought:cucumber-reporting library with advanced features
 */
@Log4j2
public class CucumberReportGenerator {

    private static final String PROJECT_NAME = "Step-Up Authentication Service";
    private static final String BUILD_NUMBER = getCurrentTimestamp();
    private static final String REPORT_TITLE = "🥒 Cucumber Test Report";

    // Report configuration
    private static final boolean PARALLEL_TESTING = false;
    private static final boolean RUN_WITH_JENKINS = false;
    private static final boolean TRENDS_ENABLED = true;
    
    /**
     * Generate enhanced HTML report from cucumber JSON file with advanced features
     *
     * @param jsonReportPath Path to cucumber JSON report file
     * @param outputDirectory Directory where HTML report will be generated
     */
    public static void generateReport(String jsonReportPath, String outputDirectory) {
        generateAdvancedReport(Arrays.asList(jsonReportPath), outputDirectory, createDefaultConfiguration(outputDirectory));
    }

    /**
     * Generate advanced HTML report with multiple JSON files and custom configuration
     *
     * @param jsonFiles List of JSON report files
     * @param outputDirectory Output directory
     * @param customConfig Custom configuration
     */
    public static void generateAdvancedReport(List<String> jsonFiles, String outputDirectory, Configuration customConfig) {
        try {
            log.info("🚀 Starting enhanced Cucumber report generation...");

            // Validate input files
            validateJsonFiles(jsonFiles);

            // Create output directory
            createOutputDirectory(outputDirectory);

            // Generate the report
            ReportBuilder reportBuilder = new ReportBuilder(jsonFiles, customConfig);
            reportBuilder.generateReports();

            // Post-processing
            enhanceGeneratedReport(outputDirectory);

            log.info("✅ Enhanced Cucumber HTML report generated successfully!");
            log.info("📊 Main report: {}/cucumber-html-reports/overview-features.html", outputDirectory);
            log.info("📈 Features: {}/cucumber-html-reports/overview-features.html", outputDirectory);
            log.info("👣 Steps: {}/cucumber-html-reports/overview-steps.html", outputDirectory);
            log.info("🏷️ Tags: {}/cucumber-html-reports/overview-tags.html", outputDirectory);
            log.info("❌ Failures: {}/cucumber-html-reports/overview-failures.html", outputDirectory);

        } catch (Exception e) {
            log.error("❌ Failed to generate enhanced cucumber report", e);
            throw new RuntimeException("Failed to generate enhanced cucumber report", e);
        }
    }
    
    /**
     * Generate report with default paths and enhanced features
     */
    public static void generateDefaultReport() {
        String jsonPath = "build/reports/cucumber-report.json";
        String outputPath = "build/reports/cucumber-html-reports";

        // Find all JSON files in reports directory
        List<String> jsonFiles = findAllJsonReports("build/reports");

        if (jsonFiles.isEmpty()) {
            jsonFiles.add(jsonPath); // Fallback to default
        }

        Configuration config = createEnhancedConfiguration(outputPath);
        generateAdvancedReport(jsonFiles, outputPath, config);
    }

    /**
     * Generate report with multiple JSON files from directory
     */
    public static void generateReportFromDirectory(String reportsDirectory, String outputDirectory) {
        List<String> jsonFiles = findAllJsonReports(reportsDirectory);
        Configuration config = createEnhancedConfiguration(outputDirectory);
        generateAdvancedReport(jsonFiles, outputDirectory, config);
    }

    /**
     * Main method for standalone execution with enhanced options
     */
    public static void main(String[] args) {
        try {
            if (args.length >= 2) {
                if (args[0].equals("--directory")) {
                    generateReportFromDirectory(args[1], args.length > 2 ? args[2] : "build/reports/cucumber-html-reports");
                } else {
                    generateReport(args[0], args[1]);
                }
            } else {
                generateDefaultReport();
            }
        } catch (Exception e) {
            log.error("❌ Error in main execution", e);
            System.exit(1);
        }
    }

    // ==================== HELPER METHODS ====================

    /**
     * Create default configuration with basic settings
     */
    private static Configuration createDefaultConfiguration(String outputDirectory) {
        File reportOutputDirectory = new File(outputDirectory);
        Configuration configuration = new Configuration(reportOutputDirectory, PROJECT_NAME);

        // Basic settings
        configuration.setBuildNumber(BUILD_NUMBER);
        configuration.setSortingMethod(SortingMethod.NATURAL);

        // Add basic classifications
        addBasicClassifications(configuration);

        return configuration;
    }

    /**
     * Create enhanced configuration with advanced features
     */
    private static Configuration createEnhancedConfiguration(String outputDirectory) {
        File reportOutputDirectory = new File(outputDirectory);
        Configuration configuration = new Configuration(reportOutputDirectory, PROJECT_NAME);

        // Enhanced settings
        configuration.setBuildNumber(BUILD_NUMBER);
        configuration.setSortingMethod(SortingMethod.NATURAL);

        // Add comprehensive classifications
        addEnhancedClassifications(configuration);

        return configuration;
    }

    /**
     * Add basic system classifications
     */
    private static void addBasicClassifications(Configuration configuration) {
        configuration.addClassifications("Platform", "Spring Boot");
        configuration.addClassifications("Environment", "Test");
        configuration.addClassifications("Java Version", System.getProperty("java.version"));
        configuration.addClassifications("OS", System.getProperty("os.name"));
    }

    /**
     * Add enhanced classifications with more details
     */
    private static void addEnhancedClassifications(Configuration configuration) {
        // Basic info
        addBasicClassifications(configuration);

        // Enhanced info
        configuration.addClassifications("Report Generated", getCurrentTimestamp());
        configuration.addClassifications("User", System.getProperty("user.name"));
        configuration.addClassifications("Working Directory", System.getProperty("user.dir"));
        configuration.addClassifications("Java Vendor", System.getProperty("java.vendor"));
        configuration.addClassifications("OS Architecture", System.getProperty("os.arch"));
        configuration.addClassifications("Available Processors", String.valueOf(Runtime.getRuntime().availableProcessors()));
        configuration.addClassifications("Max Memory", formatBytes(Runtime.getRuntime().maxMemory()));
        configuration.addClassifications("Framework", "Cucumber + Spring Boot");
        configuration.addClassifications("Report Library", "net.masterthought:cucumber-reporting:5.8.1");
    }

    /**
     * Validate that JSON files exist and are readable
     */
    private static void validateJsonFiles(List<String> jsonFiles) {
        for (String jsonFile : jsonFiles) {
            File file = new File(jsonFile);
            if (!file.exists()) {
                log.warn("⚠️ JSON file not found: {}", jsonFile);
            } else if (!file.canRead()) {
                log.warn("⚠️ Cannot read JSON file: {}", jsonFile);
            } else {
                log.info("✅ Found JSON report: {} ({})", jsonFile, formatBytes(file.length()));
            }
        }
    }

    /**
     * Create output directory if it doesn't exist
     */
    private static void createOutputDirectory(String outputDirectory) {
        File dir = new File(outputDirectory);
        if (!dir.exists()) {
            boolean created = dir.mkdirs();
            if (created) {
                log.info("📁 Created output directory: {}", outputDirectory);
            } else {
                log.warn("⚠️ Failed to create output directory: {}", outputDirectory);
            }
        }
    }

    /**
     * Find all JSON report files in a directory
     */
    private static List<String> findAllJsonReports(String directory) {
        List<String> jsonFiles = new ArrayList<>();
        try {
            Path dirPath = Paths.get(directory);
            if (Files.exists(dirPath) && Files.isDirectory(dirPath)) {
                jsonFiles = Files.walk(dirPath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".json"))
                    .filter(path -> path.toString().contains("cucumber"))
                    .map(Path::toString)
                    .collect(Collectors.toList());

                log.info("🔍 Found {} JSON report files in {}", jsonFiles.size(), directory);
                jsonFiles.forEach(file -> log.debug("   📄 {}", file));
            }
        } catch (IOException e) {
            log.warn("⚠️ Error scanning directory {}: {}", directory, e.getMessage());
        }
        return jsonFiles;
    }

    /**
     * Enhance the generated report with additional features
     */
    private static void enhanceGeneratedReport(String outputDirectory) {
        try {
            // Add custom CSS or JavaScript if needed
            addCustomStyling(outputDirectory);

            // Generate summary statistics
            generateSummaryStats(outputDirectory);

            log.info("🎨 Enhanced report with custom features");
        } catch (Exception e) {
            log.warn("⚠️ Failed to enhance report: {}", e.getMessage());
        }
    }

    /**
     * Add custom styling to the report
     */
    private static void addCustomStyling(String outputDirectory) {
        try {
            log.info("🎨 Adding enhanced styling and charts to report");

            // Copy enhanced CSS theme
            copyEnhancedTheme(outputDirectory);

            // Copy enhanced JavaScript
            copyEnhancedJavaScript(outputDirectory);

            // Copy enhanced HTML template
            copyEnhancedTemplate(outputDirectory);

            log.info("✅ Enhanced styling applied successfully");
        } catch (Exception e) {
            log.warn("⚠️ Failed to add enhanced styling: {}", e.getMessage());
        }
    }

    /**
     * Copy enhanced CSS theme to report directory
     */
    private static void copyEnhancedTheme(String outputDirectory) {
        try {
            Path sourceTheme = Paths.get("src/test/resources/cucumber-themes/professional-theme.css");
            Path targetCssDir = Paths.get(outputDirectory, "cucumber-html-reports", "css");
            Files.createDirectories(targetCssDir);

            Path targetTheme = targetCssDir.resolve("enhanced-theme.css");
            if (Files.exists(sourceTheme)) {
                Files.copy(sourceTheme, targetTheme, StandardCopyOption.REPLACE_EXISTING);
                log.debug("📄 Enhanced CSS theme copied to: {}", targetTheme);
            }
        } catch (Exception e) {
            log.warn("⚠️ Failed to copy enhanced theme: {}", e.getMessage());
        }
    }

    /**
     * Copy enhanced JavaScript to report directory
     */
    private static void copyEnhancedJavaScript(String outputDirectory) {
        try {
            Path sourceJs = Paths.get("src/test/resources/cucumber-themes/enhanced-charts.js");
            Path targetJsDir = Paths.get(outputDirectory, "cucumber-html-reports", "js");
            Files.createDirectories(targetJsDir);

            Path targetJs = targetJsDir.resolve("enhanced-charts.js");
            if (Files.exists(sourceJs)) {
                Files.copy(sourceJs, targetJs, StandardCopyOption.REPLACE_EXISTING);
                log.debug("📄 Enhanced JavaScript copied to: {}", targetJs);
            }
        } catch (Exception e) {
            log.warn("⚠️ Failed to copy enhanced JavaScript: {}", e.getMessage());
        }
    }

    /**
     * Copy enhanced HTML template to report directory
     */
    private static void copyEnhancedTemplate(String outputDirectory) {
        try {
            Path sourceTemplate = Paths.get("src/test/resources/cucumber-themes/enhanced-report-template.html");
            Path targetDir = Paths.get(outputDirectory, "cucumber-html-reports");
            Files.createDirectories(targetDir);

            Path targetTemplate = targetDir.resolve("enhanced-overview.html");
            if (Files.exists(sourceTemplate)) {
                Files.copy(sourceTemplate, targetTemplate, StandardCopyOption.REPLACE_EXISTING);
                log.debug("📄 Enhanced HTML template copied to: {}", targetTemplate);
            }
        } catch (Exception e) {
            log.warn("⚠️ Failed to copy enhanced template: {}", e.getMessage());
        }
    }

    /**
     * Generate additional summary statistics
     */
    private static void generateSummaryStats(String outputDirectory) {
        // This could generate additional statistics files
        log.debug("📊 Generating summary statistics");
    }

    /**
     * Get current timestamp for build number
     */
    private static String getCurrentTimestamp() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * Format bytes to human readable format
     */
    private static String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }

    /**
     * Print report generation summary
     */
    public static void printSummary(String outputDirectory) {
        log.info("📋 ==================== CUCUMBER REPORT SUMMARY ====================");
        log.info("🎯 Project: {}", PROJECT_NAME);
        log.info("🏗️ Build: {}", BUILD_NUMBER);
        log.info("📁 Output: {}", outputDirectory);
        log.info("🌐 Main Report: {}/cucumber-html-reports/overview-features.html", outputDirectory);
        log.info("📊 Features: {}/cucumber-html-reports/overview-features.html", outputDirectory);
        log.info("👣 Steps: {}/cucumber-html-reports/overview-steps.html", outputDirectory);
        log.info("🏷️ Tags: {}/cucumber-html-reports/overview-tags.html", outputDirectory);
        log.info("❌ Failures: {}/cucumber-html-reports/overview-failures.html", outputDirectory);
        log.info("================================================================");
    }
}
