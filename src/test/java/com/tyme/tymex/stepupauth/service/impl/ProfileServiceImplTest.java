package com.tyme.tymex.stepupauth.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;


import com.tyme.tymex.stepupauth.domain.ProfileInfo;
import com.tyme.tymex.stepupauth.infra.connector.ClientProfileConnector;
import com.tyme.tymex.stepupauth.infra.connector.exception.InternalMicroServiceException;
import com.tyme.tymex.stepupauth.infra.connector.model.ProfileDetailResponse;
import com.tyme.tymex.stepupauth.infra.connector.model.profile.ProfilePhoneData;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

@ExtendWith(MockitoExtension.class)
class ProfileServiceImplTest {

  @InjectMocks
  private ProfileServiceImpl profileService;

  @Mock
  private ClientProfileConnector clientProfileConnector;

  private String profileId;

  @BeforeEach
  void setUp() {
    profileId = "test-id";
  }

  @Test
  void shouldThrowProfileNotFoundException_whenGetProfileInfo_and4xxError() {
    InternalMicroServiceException profileException = new InternalMicroServiceException(
        HttpStatus.BAD_REQUEST,
        "{\"errors\":[{\"errorCode\":\"0106002\",\"errorMessage\":\"Profile not found\",\"unifiedErrorCode\":\"2013002\"}]}\n",
        null);
    when(clientProfileConnector.getProfileDetail(any())).thenThrow(profileException);

    var exception = assertThrows(DomainException.class,
        () -> profileService.getProfileInfo(profileId));

    assertEquals(ErrorCode.PROFILE_NOT_FOUND, exception.getErrorCode());
    verify(clientProfileConnector, times(1)).getProfileDetail(profileId);
  }

  @Test
  void shouldThrowException_whenGetProfileInfo_and5xxError() {
    InternalMicroServiceException profileException = new InternalMicroServiceException(
        HttpStatus.INTERNAL_SERVER_ERROR, "{}", null);
    when(clientProfileConnector.getProfileDetail(profileId)).thenThrow(profileException);

    var exception = assertThrows(
        InternalMicroServiceException.class, () -> profileService.getProfileInfo(profileId));

    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getHttpStatus());
    verify(clientProfileConnector, times(1)).getProfileDetail(profileId);
  }


  @Test
  void shouldThrowProfileNotFoundException_whenGetProfileInfo_andProfileDetailIsNull() {
    when(clientProfileConnector.getProfileDetail(profileId)).thenReturn(null);

    DomainException thrownException = assertThrows(DomainException.class,
        () -> profileService.getProfileInfo(profileId));

    assertEquals(ErrorCode.PROFILE_NOT_FOUND, thrownException.getErrorCode());
    verify(clientProfileConnector, times(1)).getProfileDetail(profileId);
  }

  @Test
  void shouldThrowProfileNotFoundException_whenGetProfileInfo_andProfileDetailIdIsNull() {
    ProfileDetailResponse profileResponse = ProfileDetailResponse.builder().build();
    when(clientProfileConnector.getProfileDetail(profileId)).thenReturn(profileResponse);

    DomainException thrownException = assertThrows(DomainException.class,
        () -> profileService.getProfileInfo(profileId));

    assertEquals(ErrorCode.PROFILE_NOT_FOUND, thrownException.getErrorCode());
    verify(clientProfileConnector, times(1)).getProfileDetail(profileId);
  }

  @Test
  void shouldSuccess_whenGetProfileInfo() {
    ProfileDetailResponse profileResponse = ProfileDetailResponse
        .builder()
        .id(profileId)
        .personPhoneData(ProfilePhoneData.builder().dialCode("+61").phoneNumber("01234").build())
        .build();
    when(clientProfileConnector.getProfileDetail(profileId)).thenReturn(profileResponse);

    ProfileInfo profileInfo = profileService.getProfileInfo(profileId);

    assertNotNull(profileInfo);
    assertEquals(profileId, profileInfo.id());
    verify(clientProfileConnector, times(1)).getProfileDetail(profileId);
  }

}