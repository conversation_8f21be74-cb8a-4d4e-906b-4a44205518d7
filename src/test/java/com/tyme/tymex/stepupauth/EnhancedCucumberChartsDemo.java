package com.tyme.tymex.stepupauth;

import com.tyme.tymex.stepupauth.utils.CucumberReportGenerator;
import lombok.extern.log4j.Log4j2;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 📊 Enhanced Cucumber Charts Demo
 * 
 * Demonstrates the new enhanced reporting features with:
 * - 🥧 Interactive Pie Charts
 * - 📊 Animated Bar Charts  
 * - 📈 SVG Line Charts
 * - 🎯 Gauge Meters
 * - 🔥 Activity Heatmaps
 * - ⚡ Sparkline Charts
 * - 📊 Comparison Charts
 * - ⏱️ Timeline Views
 * - 💬 Interactive Tooltips
 * - 📱 Responsive Design
 * - 🌙 Dark Mode Support
 */
@Log4j2
public class EnhancedCucumberChartsDemo {

    private static final String JSON_REPORT_PATH = "build/reports/cucumber-report.json";
    private static final String OUTPUT_PATH = "build/reports/enhanced-cucumber-charts-demo";

    public static void main(String[] args) {
        log.info("🚀 Starting Enhanced Cucumber Charts Demo...");
        
        try {
            // Check if we have test data
            if (hasTestData()) {
                log.info("✅ Found existing test data, generating enhanced report...");
                generateEnhancedReport();
            } else {
                log.info("📝 No test data found, creating sample data...");
                createSampleData();
                generateEnhancedReport();
            }
            
            // Show demo features
            showDemoFeatures();
            
            // Open report in browser
            openReportInBrowser();
            
        } catch (Exception e) {
            log.error("❌ Demo failed: {}", e.getMessage(), e);
        }
    }

    /**
     * Check if we have existing test data
     */
    private static boolean hasTestData() {
        File jsonFile = new File(JSON_REPORT_PATH);
        return jsonFile.exists() && jsonFile.length() > 0;
    }

    /**
     * Create sample test data for demo
     */
    private static void createSampleData() {
        try {
            log.info("🎨 Creating sample test data for enhanced charts demo...");
            
            String sampleJson = createSampleJsonData();
            
            // Ensure directory exists
            Path jsonPath = Paths.get(JSON_REPORT_PATH);
            Files.createDirectories(jsonPath.getParent());
            
            // Write sample data
            Files.write(jsonPath, sampleJson.getBytes());
            
            log.info("✅ Sample data created: {} ({} bytes)", 
                JSON_REPORT_PATH, Files.size(jsonPath));
                
        } catch (Exception e) {
            log.error("❌ Failed to create sample data: {}", e.getMessage());
        }
    }

    /**
     * Generate enhanced report with all chart features
     */
    private static void generateEnhancedReport() {
        try {
            log.info("📊 Generating enhanced Cucumber report with charts...");
            
            // Generate using enhanced generator
            CucumberReportGenerator.generateReport(JSON_REPORT_PATH, OUTPUT_PATH);
            
            log.info("✅ Enhanced report generated successfully!");
            log.info("📍 Location: {}/cucumber-html-reports/", OUTPUT_PATH);
            
        } catch (Exception e) {
            log.error("❌ Failed to generate enhanced report: {}", e.getMessage());
        }
    }

    /**
     * Show all demo features
     */
    private static void showDemoFeatures() {
        log.info("🎯 Enhanced Cucumber Charts Demo Features:");
        log.info("");
        log.info("📊 CHARTS & VISUALIZATIONS:");
        log.info("   🥧 Pie Charts        - Interactive with animations");
        log.info("   📊 Bar Charts        - Progressive loading with shimmer");
        log.info("   📈 Line Charts       - SVG-based with data points");
        log.info("   🎯 Gauge Charts      - Animated needle rotation");
        log.info("   🔥 Heatmaps          - Activity calendar view");
        log.info("   ⚡ Sparklines        - Inline mini charts");
        log.info("   📊 Comparison        - Current vs previous runs");
        log.info("   ⏱️ Timeline          - Execution flow visualization");
        log.info("");
        log.info("🎨 INTERACTIVE FEATURES:");
        log.info("   💬 Tooltips          - Hover information");
        log.info("   🎯 Data Cards        - Metric summaries");
        log.info("   📈 Trend Indicators  - Up/down/stable arrows");
        log.info("   🔢 Animated Numbers  - Counting animations");
        log.info("   🎨 Hover Effects     - Smooth transitions");
        log.info("");
        log.info("📱 RESPONSIVE DESIGN:");
        log.info("   📱 Mobile Support    - Touch-friendly interface");
        log.info("   🌙 Dark Mode         - Automatic theme switching");
        log.info("   📊 Flexible Layouts  - Adaptive grid system");
        log.info("   ⚡ Performance       - Optimized animations");
        log.info("");
    }

    /**
     * Open the enhanced report in browser
     */
    private static void openReportInBrowser() {
        try {
            // Try enhanced report first
            Path enhancedReport = Paths.get(OUTPUT_PATH, "cucumber-html-reports", "enhanced-overview.html");
            Path standardReport = Paths.get(OUTPUT_PATH, "cucumber-html-reports", "overview-features.html");
            
            Path reportToOpen = Files.exists(enhancedReport) ? enhancedReport : standardReport;
            
            if (Files.exists(reportToOpen)) {
                log.info("🌐 Opening enhanced report in browser...");
                log.info("📊 Report: {}", reportToOpen.getFileName());
                
                String os = System.getProperty("os.name").toLowerCase();
                ProcessBuilder pb;
                
                if (os.contains("mac")) {
                    pb = new ProcessBuilder("open", reportToOpen.toString());
                } else if (os.contains("windows")) {
                    pb = new ProcessBuilder("cmd", "/c", "start", reportToOpen.toString());
                } else {
                    pb = new ProcessBuilder("xdg-open", reportToOpen.toString());
                }
                
                pb.start();
                log.info("✅ Report opened in default browser");
                
            } else {
                log.warn("⚠️ Report file not found: {}", reportToOpen);
            }
            
        } catch (Exception e) {
            log.warn("⚠️ Failed to open report in browser: {}", e.getMessage());
            log.info("📍 Manual open: {}/cucumber-html-reports/enhanced-overview.html", OUTPUT_PATH);
        }
    }

    /**
     * Create sample JSON data for demo
     */
    private static String createSampleJsonData() {
        return """
        [
          {
            "line": 1,
            "elements": [
              {
                "line": 3,
                "name": "User authentication with OTP",
                "description": "",
                "id": "authentication;user-authentication-with-otp",
                "type": "scenario",
                "keyword": "Scenario",
                "steps": [
                  {
                    "result": {
                      "duration": 1500000000,
                      "status": "passed"
                    },
                    "line": 4,
                    "name": "user initiates authentication with OTP",
                    "match": {
                      "location": "StepDefinitions.user_initiates_authentication_with_otp()"
                    },
                    "keyword": "Given "
                  },
                  {
                    "result": {
                      "duration": 2000000000,
                      "status": "passed"
                    },
                    "line": 5,
                    "name": "OTP is sent to user device",
                    "match": {
                      "location": "StepDefinitions.otp_is_sent_to_user_device()"
                    },
                    "keyword": "When "
                  },
                  {
                    "result": {
                      "duration": 1000000000,
                      "status": "passed"
                    },
                    "line": 6,
                    "name": "user should be authenticated successfully",
                    "match": {
                      "location": "StepDefinitions.user_should_be_authenticated_successfully()"
                    },
                    "keyword": "Then "
                  }
                ]
              },
              {
                "line": 8,
                "name": "Session validation with device bio",
                "description": "",
                "id": "authentication;session-validation-with-device-bio",
                "type": "scenario",
                "keyword": "Scenario",
                "steps": [
                  {
                    "result": {
                      "duration": 1800000000,
                      "status": "passed"
                    },
                    "line": 9,
                    "name": "user has valid session",
                    "match": {
                      "location": "StepDefinitions.user_has_valid_session()"
                    },
                    "keyword": "Given "
                  },
                  {
                    "result": {
                      "duration": 2500000000,
                      "status": "failed",
                      "error_message": "Device bio validation failed"
                    },
                    "line": 10,
                    "name": "device bio validation is performed",
                    "match": {
                      "location": "StepDefinitions.device_bio_validation_is_performed()"
                    },
                    "keyword": "When "
                  },
                  {
                    "result": {
                      "status": "skipped"
                    },
                    "line": 11,
                    "name": "session should remain valid",
                    "match": {
                      "location": "StepDefinitions.session_should_remain_valid()"
                    },
                    "keyword": "Then "
                  }
                ]
              }
            ],
            "name": "Step-up Authentication",
            "description": "Enhanced authentication scenarios with multiple factors",
            "id": "authentication",
            "keyword": "Feature",
            "uri": "src/test/resources/features/authentication.feature",
            "tags": [
              {
                "line": 1,
                "name": "@authentication"
              }
            ]
          }
        ]
        """;
    }

    /**
     * Format file size for display
     */
    private static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }
}
